{"name": "website-aadvik", "version": "0.1.10", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:dev": "next build", "start": "next start", "lint": "next lint", "version:patch": "node scripts/update-version.js patch", "version:minor": "node scripts/update-version.js minor", "version:major": "node scripts/update-version.js major", "test:version": "node scripts/test-version.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.1", "aos": "^2.3.4", "formik": "^2.4.6", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "reactbits-animation": "^1.0.0", "swiper": "^11.2.8", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/aos": "^3.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "sass": "^1.89.1", "tailwindcss": "^4", "typescript": "^5"}}