<svg width="1440" height="523" viewBox="0 0 1440 523" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_2802_715)">
<ellipse cx="920.743" cy="689.028" rx="920.743" ry="689.028" transform="matrix(1 -1.03497e-07 -7.86542e-08 -1 -207.906 1453.04)" fill="url(#paint0_radial_2802_715)"/>
</g>
<defs>
<filter id="filter0_f_2802_715" x="-282.718" y="0.172211" width="1991.11" height="1527.68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="37.4061" result="effect1_foregroundBlur_2802_715"/>
</filter>
<radialGradient id="paint0_radial_2802_715" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(920.743 689.028) rotate(90) scale(689.028 920.743)">
<stop offset="0.201923" stop-color="#05050D"/>
<stop offset="0.480769" stop-color="#021F2E"/>
<stop offset="0.711538" stop-color="#021F2E"/>
<stop offset="1" stop-color="#022C42"/>
</radialGradient>
</defs>
</svg>
