<svg width="1367" height="412" viewBox="0 0 1367 412" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_2802_582)">
<ellipse cx="513.51" cy="345.823" rx="513.51" ry="345.823" transform="matrix(-0.999956 0.00934512 0.00934516 0.999956 1213.73 -358.271)" fill="#83BCD9" style="mix-blend-mode:soft-light"/>
</g>
<g filter="url(#filter1_f_2802_582)">
<ellipse cx="611" cy="336.463" rx="611" ry="336.463" transform="matrix(-0.999956 0.00934513 0.00934516 0.999956 1293.84 -347.129)" fill="url(#paint0_radial_2802_582)"/>
</g>
<defs>
<filter id="filter0_f_2802_582" x="115.164" y="-428.318" width="1176.62" height="841.308" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="37.4061" result="effect1_foregroundBlur_2802_582"/>
</filter>
<filter id="filter1_f_2802_582" x="0.215179" y="-416.281" width="1371.59" height="822.618" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="37.4061" result="effect1_foregroundBlur_2802_582"/>
</filter>
<radialGradient id="paint0_radial_2802_582" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(611 336.463) rotate(90) scale(336.463 611)">
<stop offset="0.201923" stop-color="#05050D"/>
<stop offset="0.480769" stop-color="#021F2E"/>
<stop offset="0.711538" stop-color="#021F2E"/>
<stop offset="1" stop-color="#022C42"/>
</radialGradient>
</defs>
</svg>
