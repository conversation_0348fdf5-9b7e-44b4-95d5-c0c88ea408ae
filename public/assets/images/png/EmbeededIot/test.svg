<svg width="1436" height="715" viewBox="0 0 1436 715" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_2802_482" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1440" height="715">
<rect width="1440" height="715" transform="matrix(-1 0 0 1 1440 0)" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_2802_482)">
<g filter="url(#filter0_f_2802_482)">
<ellipse cx="513.51" cy="622.83" rx="513.51" ry="622.83" transform="matrix(1 -1.29792e-07 -6.1455e-08 -1 184.25 1365.14)" fill="#83BCD9" style="mix-blend-mode:soft-light"/>
</g>
<g filter="url(#filter1_f_2802_482)">
<ellipse cx="611" cy="605.972" rx="611" ry="605.972" transform="matrix(1 -1.37163e-07 -5.93485e-08 -1 104.246 1343.72)" fill="url(#paint0_radial_2802_482)"/>
</g>
</g>
<defs>
<filter id="filter0_f_2802_482" x="109.438" y="44.6703" width="1176.64" height="1395.28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="37.4061" result="effect1_foregroundBlur_2802_482"/>
</filter>
<filter id="filter1_f_2802_482" x="29.4339" y="56.9691" width="1371.62" height="1361.57" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="37.4061" result="effect1_foregroundBlur_2802_482"/>
</filter>
<radialGradient id="paint0_radial_2802_482" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(611 605.972) rotate(90) scale(605.972 611)">
<stop offset="0.201923" stop-color="#05050D"/>
<stop offset="0.480769" stop-color="#021F2E"/>
<stop offset="0.711538" stop-color="#021F2E"/>
<stop offset="1" stop-color="#022C42"/>
</radialGradient>
</defs>
</svg>
