<svg width="1440" height="518" viewBox="0 0 1440 518" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_3070_680" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="-430" y="0" width="2264" height="518">
<rect width="2263.91" height="518" transform="matrix(-1 0 0 1 1833.99 0)" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_3070_680)">
<g filter="url(#filter0_f_3070_680)">
<ellipse cx="807.322" cy="451.225" rx="807.322" ry="451.225" transform="matrix(1 -5.98099e-08 -1.33362e-07 -1 -140.262 989.016)" fill="#83BCD9" style="mix-blend-mode:soft-light"/>
</g>
<g filter="url(#filter1_f_3070_680)">
<ellipse cx="960.592" cy="439.012" rx="960.592" ry="439.012" transform="matrix(1 -6.32069e-08 -1.2879e-07 -1 -266.047 973.492)" fill="url(#paint0_radial_3070_680)"/>
</g>
</g>
<defs>
<filter id="filter0_f_3070_680" x="-215.074" y="11.7542" width="1764.27" height="1052.07" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="37.4061" result="effect1_foregroundBlur_3070_680"/>
</filter>
<filter id="filter1_f_3070_680" x="-340.859" y="20.6566" width="2070.81" height="1027.65" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="37.4061" result="effect1_foregroundBlur_3070_680"/>
</filter>
<radialGradient id="paint0_radial_3070_680" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(960.592 439.012) rotate(90) scale(439.012 960.592)">
<stop offset="0.201923" stop-color="#05050D"/>
<stop offset="0.480769" stop-color="#021F2E"/>
<stop offset="0.711538" stop-color="#021F2E"/>
<stop offset="1" stop-color="#022C42"/>
</radialGradient>
</defs>
</svg>
