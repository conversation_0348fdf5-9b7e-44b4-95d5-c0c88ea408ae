name: Deploy to Firebase Hosting

on:
  push:
    branches: [ develop ]
  pull_request:
    branches: [ develop ]
    types: [ closed ]

env:
  NODE_VERSION: '18'

jobs:
  build-and-deploy:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.merged == true)
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0
        clean: true

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm install --legacy-peer-deps

    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"

    - name: Update semantic version
      id: version
      run: |
        # Skip version update if commit message contains [skip ci] to prevent loops
        if [[ "${{ github.event.head_commit.message }}" == *"[skip ci]"* ]]; then
          echo "Skipping version update due to [skip ci] in commit message"
          echo "skip_version=true" >> $GITHUB_OUTPUT
          exit 0
        fi

        # Determine version bump type based on commit message or default to patch
        if [[ "${{ github.event.head_commit.message }}" == *"BREAKING CHANGE"* ]] || [[ "${{ github.event.head_commit.message }}" == *"major:"* ]]; then
          VERSION_TYPE="major"
        elif [[ "${{ github.event.head_commit.message }}" == *"feat:"* ]] || [[ "${{ github.event.head_commit.message }}" == *"minor:"* ]]; then
          VERSION_TYPE="minor"
        else
          VERSION_TYPE="patch"
        fi

        echo "Version bump type: $VERSION_TYPE"
        node scripts/update-version.js $VERSION_TYPE
        echo "skip_version=false" >> $GITHUB_OUTPUT

    - name: Commit version update
      if: steps.version.outputs.skip_version != 'true'
      run: |
        git add package.json
        if git diff --staged --quiet; then
          echo "No version changes to commit"
        else
          git commit -m "chore: bump version to ${{ steps.version.outputs.new_version }} [skip ci]"

          # Retry push with pull/rebase up to 3 times
          for i in {1..3}; do
            echo "Push attempt $i/3"

            if git push origin ${{ github.ref_name }}; then
              echo "✅ Successfully pushed version update"
              break
            else
              echo "⚠️ Push failed, attempting to sync with remote..."

              # Fetch latest changes
              git fetch origin ${{ github.ref_name }}

              # Try rebase first, then merge if rebase fails
              if git rebase origin/${{ github.ref_name }}; then
                echo "✅ Rebased successfully"
              else
                echo "⚠️ Rebase failed, trying merge..."
                git rebase --abort 2>/dev/null || true
                git merge origin/${{ github.ref_name }} --no-edit
              fi

              if [ $i -eq 3 ]; then
                echo "❌ Failed to push after 3 attempts"
                exit 1
              fi
            fi
          done
        fi

    - name: Build application
      run: npm run build:dev
      env:
        NODE_ENV: production

    - name: Install Firebase CLI
      run: npm install -g firebase-tools

    - name: Verify build and Firebase config
      run: |
        echo "Verifying repository structure..."
        ls -la
        echo "Checking for Firebase configuration files..."
        test -f firebase.json && echo "✓ firebase.json found" || echo "✗ firebase.json missing"
        test -f .firebaserc && echo "✓ .firebaserc found" || echo "✗ .firebaserc missing"
        echo "Checking for build directory..."
        test -d build_dir && echo "✓ build_dir found" || echo "✗ build_dir missing"
        echo "Current version in package.json:"
        grep '"version"' package.json

    - name: Deploy to Firebase Hosting
      run: |
        # Verify required files exist
        if [ ! -f "firebase.json" ]; then
          echo "Error: firebase.json not found!"
          exit 1
        fi

        if [ ! -d "build_dir" ]; then
          echo "Error: build_dir directory not found!"
          exit 1
        fi

        # Show Firebase version and login status
        firebase --version

        # Deploy to Firebase
        firebase deploy --only hosting --project websitedemo-32483 --non-interactive
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

    - name: Commit version update
      if: success()
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add package.json
        if git diff --staged --quiet; then
          echo "No changes to commit"
        else
          git commit -m "chore: bump version to ${{ steps.version.outputs.new_version }} [skip ci]"
          git push
        fi
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Create deployment summary
      if: always()
      run: |
        echo "## Deployment Summary 🚀" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "- **Project**: websitedemo-32483" >> $GITHUB_STEP_SUMMARY
        echo "- **Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        if [ "${{ steps.version.outputs.new_version }}" != "" ]; then
          echo "- **Version**: ${{ steps.version.outputs.old_version }} → ${{ steps.version.outputs.new_version }}" >> $GITHUB_STEP_SUMMARY
        fi
        echo "- **Status**: ${{ job.status }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        if [ "${{ job.status }}" == "success" ]; then
          echo "✅ Deployment completed successfully!" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "🌐 **Live URL**: https://websitedemo-32483.web.app" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ Deployment failed. Check the logs above for details." >> $GITHUB_STEP_SUMMARY
        fi
