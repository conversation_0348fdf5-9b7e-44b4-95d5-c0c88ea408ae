// AOS (Animate On Scroll) default configuration
import AOS from "aos";

export const initAOS = () => {
  AOS.init({
    // Global settings
    duration: 800, // Default animation duration
    easing: "ease-out", // Default timing function
    once: true, // Whether animation should happen only once
    offset: 100, // Offset (in px) from the original trigger point
    delay: 0, // Default delay before animation starts

    // Disable animation on mobile devices if performance is an issue
    // disable: window.innerWidth < 768,

    // Enable if you want animations to work on mobile rotation
    // mirror: true,
  });
};
