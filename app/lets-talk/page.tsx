"use client";

import React, { useState } from "react";
import "./LetsTalk.scss";
import { Box, Divider, Typography } from "@mui/material";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import PhoneIcon from "@mui/icons-material/Phone";
import { Mail } from "@mui/icons-material";
import Image from "next/image";
import { AttachmentIcon } from "@/public";
import { useFormik } from "formik";
import * as Yup from "yup";
import { hero_bg_ellipse } from "@/public/index";

// Define types for props where needed
interface ContactInfoCardProps {
  location: string;
  addressLine1: string;
  addressLine2: string;
  phone: string;
  email: string;
}

interface FormFieldProps {
  activeTab: string;
  messageLength: number;
  handleMessageChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  formik: any;
}

const LetsTalk: React.FC = () => {
  // State to track the active tab and message length
  const [activeTab, setActiveTab] = useState("Candidate");
  const [messageLength, setMessageLength] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Function to handle tab click
  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
    formik.resetForm();
    setSelectedFile(null);
    setMessageLength(0);
  };

  // Function to handle message change
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessageLength(e.target.value.length);
    formik.handleChange(e);
  };

  // Function to handle file change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
  };

  // Yup validation schema based on activeTab
  const getValidationSchema = (activeTab: string) => {
    const baseSchema = {
      firstName: Yup.string()
        .min(2, "First Name must be at least 2 characters")
        .required("First Name is required"),
      lastName: Yup.string()
        .min(2, "Last Name must be at least 2 characters")
        .required("Last Name is required"),
      phoneNo: Yup.string(), // No validation for phoneNo
      message: Yup.string()
        .max(500, "Message cannot exceed 500 characters")
        .required("Message is required"),
      termsAgree: Yup.boolean()
        .oneOf([true], "You must agree to the terms")
        .required("You must agree to the terms"),
      newsletterAgree: Yup.boolean(),
      company: Yup.string().required(
        activeTab === "Candidate"
          ? "Experience is required"
          : activeTab === "Aadvik Alumni"
          ? "Current Company is required"
          : "Company is required"
      ),
      industry: Yup.string().required(
        activeTab === "Candidate"
          ? "Domain is required"
          : activeTab === "Aadvik Alumni"
          ? "Current Industry is required"
          : "Industry is required"
      ),
      position: Yup.string(), // No validation for position
    };

    return Yup.object(baseSchema);
  };

  // Formik setup
  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
      company: "",
      industry: "",
      phoneNo: "",
      position: "",
      message: "",
      termsAgree: false,
      newsletterAgree: false,
    },
    validationSchema: getValidationSchema(activeTab),
    onSubmit: (values) => {
      console.log("Form Values:", {
        ...values,
        file: selectedFile ? selectedFile.name : "No file attached",
      });
      formik.resetForm();
      setSelectedFile(null);
      setMessageLength(0);
    },
  });

  // Section: Header
  const renderHeader = () => (
    <Box className="get-in-touch-header">
      <Typography className="get-in-touch-title">
        Get in Touch With Us!
      </Typography>
    </Box>
  );

  // Section: Contact Info Card (Reusable for India and Canada)
  const ContactInfoCard: React.FC<ContactInfoCardProps> = ({
    location,
    addressLine1,
    addressLine2,
    phone,
    email,
  }) => (
    <Box className="contact-info-card">
      <Box className="location-header">
        <LocationOnIcon className="icon-placeholder" />
        <Typography variant="h3">{location}</Typography>
      </Box>
      <Typography className="address-line">{addressLine1}</Typography>
      <Typography className="address-line">{addressLine2}</Typography>
      <Box className="phone-number">
        <PhoneIcon className="icon-placeholder" />
        <Typography className="phone-number-text">{phone}</Typography>
      </Box>
      <Box className="email-container">
        <Mail className="icon-placeholder" />
        <a href={`mailto:${email}`}>{email}</a>
      </Box>
    </Box>
  );

  // Section: Contact Info Section (India and Canada)
  const renderContactInfoSection = () => (
    <Box className="contact-info-section">
      <ContactInfoCard
        location="Canada"
        addressLine1="No 33, Kilkarrin Road"
        addressLine2="Brampton, Ontario L7A4C6"
        phone="+****************"
        email="<EMAIL>"
      />

      <ContactInfoCard
        location="India"
        addressLine1="#SF04, Mithra Enclave"
        addressLine2="Doddakalashandra, Bangalore - 62"
        phone="+91(80) 77052301"
        email="<EMAIL>"
      />
    </Box>
  );

  // Section: Category Buttons (Tabs)
  const renderCategoryButtons = () => (
    <Box className="category-buttons">
      {["Candidate", "Aadvik Alumni", "Customer", "Supplier"].map((tab) => (
        <button
          key={tab}
          className={`category-button ${activeTab === tab ? "active" : ""}`}
          onClick={() => handleTabClick(tab)}
        >
          {tab}
        </button>
      ))}
    </Box>
  );

  // Section: Form Fields (Dynamic based on activeTab)
  const renderFormFields = ({
    activeTab,
    messageLength,
    handleMessageChange,
    formik,
  }: FormFieldProps) => (
    <>
      <Box className="form-row">
        <Box className="form-group">
          <label htmlFor="firstName">*First Name</label>
          <input
            type="text"
            id="firstName"
            name="firstName"
            value={formik.values.firstName}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            placeholder=""
          />
          {formik.touched.firstName && formik.errors.firstName ? (
            <Typography className="error-text">
              {formik.errors.firstName}
            </Typography>
          ) : null}
        </Box>
        <Box className="form-group">
          <label htmlFor="lastName">*Last Name</label>
          <input
            type="text"
            id="lastName"
            name="lastName"
            value={formik.values.lastName}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            placeholder=""
          />
          {formik.touched.lastName && formik.errors.lastName ? (
            <Typography className="error-text">
              {formik.errors.lastName}
            </Typography>
          ) : null}
        </Box>
      </Box>

      <Box className="form-row">
        {activeTab === "Candidate" ? (
          <>
            <Box className="form-group">
              <label htmlFor="company">*Experience</label>
              <input
                type="text"
                id="company"
                name="company"
                value={formik.values.company}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder=""
              />
              {formik.touched.company && formik.errors.company ? (
                <Typography className="error-text">
                  {formik.errors.company}
                </Typography>
              ) : null}
            </Box>
            <Box className="form-group">
              <label htmlFor="industry">*Domain</label>
              <input
                type="text"
                id="industry"
                name="industry"
                value={formik.values.industry}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder=""
              />
              {formik.touched.industry && formik.errors.industry ? (
                <Typography className="error-text">
                  {formik.errors.industry}
                </Typography>
              ) : null}
            </Box>
          </>
        ) : activeTab === "Aadvik Alumni" ? (
          <>
            <Box className="form-group">
              <label htmlFor="company">*Current Company</label>
              <input
                type="text"
                id="company"
                name="company"
                value={formik.values.company}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder=""
              />
              {formik.touched.company && formik.errors.company ? (
                <Typography className="error-text">
                  {formik.errors.company}
                </Typography>
              ) : null}
            </Box>
            <Box className="form-group">
              <label htmlFor="industry">*Current Industry</label>
              <input
                type="text"
                id="industry"
                name="industry"
                value={formik.values.industry}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder=""
              />
              {formik.touched.industry && formik.errors.industry ? (
                <Typography className="error-text">
                  {formik.errors.industry}
                </Typography>
              ) : null}
            </Box>
          </>
        ) : (
          <>
            <Box className="form-group">
              <label htmlFor="company">*Company</label>
              <input
                type="text"
                id="company"
                name="company"
                value={formik.values.company}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder=""
              />
              {formik.touched.company && formik.errors.company ? (
                <Typography className="error-text">
                  {formik.errors.company}
                </Typography>
              ) : null}
            </Box>
            <Box className="form-group">
              <label htmlFor="industry">*Industry</label>
              <input
                type="text"
                id="industry"
                name="industry"
                value={formik.values.industry}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder=""
              />
              {formik.touched.industry && formik.errors.industry ? (
                <Typography className="error-text">
                  {formik.errors.industry}
                </Typography>
              ) : null}
            </Box>
          </>
        )}
      </Box>

      <Box className="form-row">
        <Box className="form-group">
          <label htmlFor="phoneNo">Email / Phone No.</label>
          <input
            type="text"
            id="phoneNo"
            name="phoneNo"
            value={formik.values.phoneNo}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            placeholder=""
          />
          {formik.touched.phoneNo && formik.errors.phoneNo ? (
            <Typography className="error-text">
              {formik.errors.phoneNo}
            </Typography>
          ) : null}
        </Box>
        <Box className="form-group">
          {activeTab === "Candidate" ? (
            <label htmlFor="position">Applied for</label>
          ) : activeTab === "Aadvik Alumni" ? (
            <label htmlFor="position">Employee ID</label>
          ) : activeTab === "Customer" ? (
            <label htmlFor="position">Designation</label>
          ) : (
            <label htmlFor="position">Position</label>
          )}
          <input
            type="text"
            id="position"
            name="position"
            value={formik.values.position}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            placeholder=""
          />
          {formik.touched.position && formik.errors.position ? (
            <Typography className="error-text">
              {formik.errors.position}
            </Typography>
          ) : null}
        </Box>
      </Box>

      <Box className="form-group-message-group">
        <label htmlFor="message">*Message</label>
        <textarea
          id="message"
          name="message"
          placeholder=""
          rows={6}
          maxLength={500}
          value={formik.values.message}
          onChange={handleMessageChange}
          onBlur={formik.handleBlur}
        ></textarea>
        <Box className="message-footer">
          <span className="char-count">{messageLength}/500</span>
          <Box className="attachment-group">
            <label htmlFor="attachment" className="attachment-label">
              <span>Attachment (2Mb)</span>
              <Image src={AttachmentIcon} alt="attachment-icon" />
            </label>
            <input
              type="file"
              id="attachment"
              onChange={handleFileChange}
              style={{ display: "none" }}
            />
          </Box>
        </Box>
      </Box>
    </>
  );

  // Section: Checkboxes and Submit Button
  const renderCheckboxesAndSubmit = () => (
    <>
      <Divider className="divider" />
      <Box className="checkbox-group">
        <input
          type="checkbox"
          id="termsAgree"
          name="termsAgree"
          checked={formik.values.termsAgree}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
        />
        <label htmlFor="termsAgree">
          *I have read and agreed with <a href="#">Terms of use</a>,{" "}
          <a href="#">Privacy Policy</a> and <a href="#">Cookie Policy</a>.
        </label>
      </Box>

      <Box className="checkbox-group">
        <input
          type="checkbox"
          id="newsletterAgree"
          name="newsletterAgree"
          checked={formik.values.newsletterAgree}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
        />
        <label htmlFor="newsletterAgree">
          Yes, please keep me updated with Aadvik news letter, events, offers,
          services and marketing activity by post, email, sms, MMS, phone,
          social media, push notifications in Apps and other means. I understand
          that I may opt out at any time.
        </label>
      </Box>

      <button type="submit" className="submit-message-button">
        Submit
      </button>
    </>
  );

  // Section: Contact Form Card (Combines tabs, form fields, checkboxes, and submit)
  const renderContactFormCard = () => (
    <Box className="contact-form-card">
      <Typography className="contact-us-title">Contact Us</Typography>
      {renderCategoryButtons()}
      <Divider className="divider" />
      <form className="contact-form" onSubmit={formik.handleSubmit}>
        {renderFormFields({
          activeTab,
          messageLength,
          handleMessageChange,
          formik,
        })}
        {renderCheckboxesAndSubmit()}
      </form>
    </Box>
  );

  // Main Render
  return (
    <Box className="get-in-touch-section">
      <Box className="ellipse-container">
        <Image src={hero_bg_ellipse} alt="Ellipse" className="ellipse-image" />
      </Box>
      {renderHeader()}
      <Box className="main-content">
        <Box className="start-header"></Box>
        {renderContactFormCard()}
        {renderContactInfoSection()}
      </Box>
    </Box>
  );
};

export default LetsTalk;
