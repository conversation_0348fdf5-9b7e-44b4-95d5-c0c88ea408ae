.get-in-touch-section {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    // background: #021F2E !important;
    width: 100%;
    margin-bottom: 200px;

    // Dotted pattern background
    // &::before {
    //   content: "";
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    //   right: 0;
    //   bottom: 0;
    //   background-image: radial-gradient(circle, #295d70 2px, #0000 1px);
    //   background-size: 20px 20px;
    //   z-index: 0;
    // }

    .start-header {
        width: 100%;
        position: absolute !important;
        top: 50%;
        min-height: 451px;
        position: relative;
        margin-top: -350px; // This creates the overlap effect

        overflow: hidden;
        z-index: -1; // Places it behind the form

        &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: radial-gradient(circle, #295d70 2px, transparent 1px);
            background-size: 20px 20px;
            z-index: 0;
        }
    }

    .ellipse-container {
        position: absolute;
        width: 100%;
        height: 100vh;
        z-index: -1;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .get-in-touch-header {
        margin-top: 20%;

        .get-in-touch-title {
            font-family: Ruluko !important;
            font-weight: 400;
            font-size: 80px;
            line-height: 30px;
            letter-spacing: 0%;
            vertical-align: middle;
            text-align: center;
            color: #ffffff;
        }
    }



    .main-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-self: center;
        align-items: center;
        // min-height: 100vh;
        position: relative;
        z-index: 1;
        // padding-top: 140px;
        margin-top: 160px;
    }

    .contact-info-section {
        flex: 0.8;
        padding: 139px 0px 0px 103px;
        display: flex;
        // flex-direction: column;
        // justify-content: center;
        gap: 180px;

        .contact-info-card {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            color: #ffffff;
            font-family: Poppins;

            .location-header {
                display: flex;
                align-items: center;
                margin-bottom: 20px;

                .icon-placeholder {
                    font-size: 28px;
                    color: #46aed7;
                    margin-right: 30px;
                }

                h3 {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 40px;
                    margin: 0;
                    color: #ffffff;
                    letter-spacing: 0.5px;
                }
            }

            .address-line {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;
                line-height: 30px;
                color: #ffffff;
                margin-left: 60px;
            }

            .phone-number {
                display: flex;
                align-items: center;
                margin: 20px 0 15px 0;

                .icon-placeholder {
                    font-size: 22px;
                    color: #46aed7;
                    margin-right: 30px;
                }

                .phone-number-text {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;
                    color: #ffffff;
                }
            }

            .email-container {
                display: flex;
                align-items: center;

                .icon-placeholder {
                    font-size: 22px;
                    color: #46aed7;
                    margin-right: 30px;
                }

                a {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;
                    color: #ffffff;
                    text-decoration: underline;

                    &:hover {
                        color: #46aed7;
                    }
                }
            }
        }
    }

    .contact-form-card {
        border-radius: 20px;
        flex: 1.3;
        background-color: rgba(217, 217, 217, 0.95);
        padding: 40px;
        // margin: 40px 70px 40px ;
        position: relative;
        z-index: 2;
        max-width: 900px;

        // backdrop-filter: blur(10px);
        .contact-us-title {
            font-family: Poppins;
            font-weight: 400;
            font-size: 40px;
            line-height: 30px;
            letter-spacing: 0%;
            background: #000000;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: left;
            margin-bottom: 30px;
            margin-left: 20px;
        }

        .category-buttons {
            display: flex;
            gap: 6.35px;
            margin-bottom: 44px;

            .category-button {
                flex: 1;
                background-color: #ffffff;
                border: 1px solid #ffffff;
                padding: 9px 0;
                font-family: Poppins;
                font-size: 20px;
                border-radius: 10px;
                font-weight: 400;
                color: #021f2e;
                cursor: pointer;
                transition: all 0.3s ease;
                white-space: nowrap;
                text-align: center;
                opacity: 0.8;

                &.active {
                    background-color: #d77d46;
                    color: #000000;
                    border-color: #d77d46;
                    opacity: 1;
                    box-shadow: 4px 4px 4px 0px #00000040;
                }
            }
        }

        .divider {
            border: none;
            border-top: 1px solid #000000;
            margin: 0 0 30px 0;
        }

        .contact-form {
            .form-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 25px;
                gap: 20px;
                gap: 20px;
            }

            .form-group {
                flex: 1;
                display: flex;
                flex-direction: column;

                label {
                    font-family: Poppins !important;
                    font-weight: 300;
                    font-size: 20px;
                    // line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    vertical-align: middle;
                    color: #000000;
                    margin-bottom: 5px;
                }

                input[type="text"],
                input[type="email"],
                input[type="tel"] {
                    background-color: #e9e9e9;
                    border: 1px solid #8c8a8a;
                    // border-radius: 6px;
                    padding: 12px 15px;
                    font-family: Poppins;
                    font-size: 14px;
                    color: #333;
                    width: 100%;
                    box-sizing: border-box;
                    transition: all 0.3s ease;

                    &:focus {
                        outline: none;
                        border-color: #d77d46;
                        box-shadow: 0 0 0 2px rgba(215, 125, 70, 0.1);
                    }

                    &::placeholder {
                        color: #999;
                    }
                }
            }
        }

        .form-group-message-group {
            margin-bottom: 25px;

            label {
                font-family: Poppins !important;
                font-weight: 300;
                font-size: 20px;
                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                color: #000000;
                margin-bottom: 8px;
                display: block;
            }

            textarea {
                background-color: #e9e9e9;
                border: 1px solid #8c8a8a;
                padding: 15px;
                font-family: Poppins;
                font-size: 14px;
                color: #333;
                width: 100%;
                box-sizing: border-box;
                resize: vertical;
                min-height: 286px;
                transition: all 0.3s ease;

                &:focus {
                    outline: none;
                    border-color: #d77d46;
                    box-shadow: 0 0 0 2px rgba(215, 125, 70, 0.1);
                }

                &::placeholder {
                    color: #999;
                }
            }

            .char-count {
                display: block;
                text-align: right;
                font-size: 12px;
                color: #666;
                margin-top: 5px;
            }

            .attachment-group {
                // margin-top: 10px;
                display: flex;
                justify-content: flex-end;
                align-items: center;

                .attachment-label {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    font-family: Poppins !important;
                    font-weight: 300;
                    font-size: 20px;
                    color: #000000;

                    span {
                        margin-right: 8px; // Space between text and icon
                    }

                    img {
                        width: 39px;
                        height: 39px;
                        vertical-align: middle;
                        transition: all 0.3s ease;
                    }
                }

                input[type="file"] {
                    display: none;
                }
            }
        }

        .checkbox-group {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            gap: 24px;

            input[type="checkbox"] {
                margin-top: 2px;
                width: 30px;
                height: 35px;
                cursor: pointer;
                accent-color: #d77d46;
                flex-shrink: 0;
            }

            label {
                font-family: Poppins;
                font-size: 20px;
                color: #000000;
                line-height: 1.4;
                flex: 1;

                a {
                    color: #2499e2;
                    text-decoration: none;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }

        .submit-message-button {
            background-color: #d77d46;
            color: #ffffff;
            border: none;
            border-radius: 25px;
            padding: 14px 60px;
            font-family: Poppins;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            display: block;
            margin: 40px auto 0 auto;
            transition: all 0.3s ease;
            min-width: 140px;

            &:hover {
                background-color: #c26b3d;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(215, 125, 70, 0.3);
            }
        }
    }


}

.error-text {
    color: #ff0000; // Pure red
    font-size: 12px !important;
    margin-top: 4px;
    font-family: Poppins;
}

@media screen and (min-width: 1200px) and (max-width: 1600px) {
    .get-in-touch-section {
        .get-in-touch-header {
            // margin: 40px 0 45px 250px;
            padding: 0 180px;
            height: 90px;

            .get-in-touch-title {
                font-size: 55px;
                line-height: 60px;
            }
        }

        .contact-info-section {
            padding: 30px 0px 0px;
            // gap: 60px;
            // width: 100%;

            .contact-info-card {
                .location-header {
                    h3 {
                        font-size: 30px;
                    }
                }

                .address-line,
                .phone-number-text,
                .email-container a {
                    font-size: 16px;
                }
            }
        }

        .contact-form-card {
            // margin: 3px 70px 30px 0;
            padding: 25px;

            .contact-us-title {
                font-size: 25px;
                margin-bottom: 12px;
            }

            .category-buttons {
                margin-bottom: 20px;

                .category-button {
                    font-size: 14px;
                }
            }

            .divider {
                margin: 14px 0;
            }

            .contact-form {
                .form-row {
                    gap: 10px;
                    margin-bottom: 12px;
                }
            }

            .form-group,
            label,
            .form-group-message-group label {
                font-size: 14px !important;
                line-height: 24px;
                margin-bottom: 4px;
            }

            input[type="text"],
            input[type="email"],
            input[type="tel"],
            textarea {
                font-size: 14px;
                padding: 10px 12px;
            }

            .form-group-message-group textarea {
                min-height: 200px;
            }

            .checkbox-group {
                label {
                    font-size: 16px;
                }
            }
        }
    }
}