import type { <PERSON><PERSON><PERSON> } from "next";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Port_Lligat_Sans,
  Ephe<PERSON>,
  Inter as InterFont,
  Ruluko,
} from "next/font/google";
import "./globals.scss";
import { Providers } from "./providers";
import Navbar from "@/components/Navbar/Navbar";
import Footer from "@/components/Footer/Footer";
import ScrollToTop from "@/components/ScrollToTop/ScrollToTop";
import CookieConsent from "@/components/CookieConsent/CookieConsent";

const poppins = Poppins({
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-poppins",
});

const prata = Prata({
  weight: ["400"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-prata",
});

const montserrat = Montserrat({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-montserrat",
});

const portLligatSans = Port_Lligat_Sans({
  weight: ["400"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-port-lligat-sans",
});

const ephesis = Ephesis({
  weight: ["400"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-ephesis",
});

const inter = InterFont({
  subsets: ["latin"],
});

const ruluko = Ruluko({
  weight: ["400"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-ruluko",
});

export const metadata: Metadata = {
  title: "Aadvik Teklabs",
  description: "Aadvik Teklabs - Innovating Tomorrow's Technology Today",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      className={`${poppins.variable} ${prata.variable} ${montserrat.variable} ${portLligatSans.variable} ${ephesis.variable}`}
    >
      <body style={{ background: "#03202F" }} className={`antialiased`}>
        <Providers>
          <Navbar />
          {children}
          <ScrollToTop />
          <Footer />
          <CookieConsent />
        </Providers>
      </body>
    </html>
  );
}
