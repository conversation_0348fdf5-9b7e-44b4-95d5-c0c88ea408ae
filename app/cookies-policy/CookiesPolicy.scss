.cookies-policy {
  min-height: 100vh;
  padding: 190px 0px 73px 0px;
  display: flex;
  justify-content: center;
  align-items: flex-start;

  .cookies-policy-container {
    background-color: #d9d9d9;
    border-radius: 20px;
    max-width: 1200px;
    width: 100%;
    padding: 63px;

    .cookies-policy-text {
      font-family: Poppins !important;
      font-weight: 400;
      font-style: Regular;
      font-size: 40px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: center;
      vertical-align: middle;
    }

    .cookies-policy-date {
      font-family: Poppins !important;
      font-weight: 400;
      font-style: Regular;
      font-size: 20px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: right;
      vertical-align: middle;
      color: #222222;
      margin-bottom: 9px;
    }

    .cookies-policy-divider {
      margin-bottom: 30px !important;
      border: 1px solid #000000;
    }

    .cookies-policy-content {
      color: #222222;
      font-family: Poppins;
      font-weight: 400;
      font-style: Regular;
      font-size: 20px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: justify;
      vertical-align: middle;

      .cookies-policy-content-confirm {
        max-width: 1283px;
        margin-top: 23px;
        margin-bottom: 47px;
      }
      .cookies-policy-section {
        max-width: 970px !important;

        p {
          margin-top: 30px;
        }

        span {
          color: #4988c8;
        }
        .cookies-policy-section-title {
          font-family: Poppins !important;
          font-weight: 400;
          font-style: Regular;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          vertical-align: middle;
          margin-top: 30px;
        }
        .cookies-policy-list {
          //   padding-left: 24px;
          margin-top: 30px;

          li {
            font-family: Poppins !important;
            font-weight: 400;
            font-style: Regular;
            font-size: 16px !important;
            line-height: 30px;
            letter-spacing: 0%;
            text-align: justify;
            vertical-align: middle;
          }
          .cookies-policy-list-item-dot {
            color: black;
            margin-right: 8px;
          }
        }
        .cookies-policy-list-item {
          //   padding-left: 24px;
          // margin-top: 30px;

          li {
            font-family: Poppins !important;
            font-weight: 400;
            font-style: Regular;
            font-size: 16px !important;
            line-height: 30px;
            letter-spacing: 0%;
            text-align: justify;
            vertical-align: middle;
          }
          .cookies-policy-list-item-dot {
            color: black;
            margin-right: 8px;
          }
        }
      }
      .understood-btn-container {
        display: flex;
        justify-content: flex-end;
        .understood-btn {
          margin-top: 12px;
          padding: 22px 51px;
          text-transform: none;
          font-family: Poppins !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 20px;
          line-height: 30px;
          color: #fff;
          letter-spacing: 0%;
          text-align: justify;
          vertical-align: middle;
          border-radius: 30px;
          background-color: #4988c8;
        }
      }
    }
  }
}
