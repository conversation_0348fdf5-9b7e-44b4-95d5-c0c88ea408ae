import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@mui/material";
import "./CookiesPolicy.scss";

const CookiesPolicy = () => {
  return (
    <Box className="cookies-policy">
      <Box className="cookies-policy-container">
        <Typography className="cookies-policy-text">Cookie Policy</Typography>

        <Typography
          variant="body2"
          align="right"
          className="cookies-policy-date"
        >
          "Effective Date : May 09, 2024
        </Typography>

        <Divider className="cookies-policy-divider" />

        <Box className="cookies-policy-content">
          <Typography>"Effective Date : May 09, 2024</Typography>

          <Box className="cookies-policy-section">
            <Typography>
              This Cookie Policy explains the types of cookies and similar
              technologies used on this website and how we use them. It also
              outlines your options for managing these technologies. We may
              update this policy from time to time. The "Effective Date" is
              mentioned at the top of the page reflects the most recent
              revision. Any changes take effect once the updated policy is
              published on the website.
            </Typography>
            <Typography variant="h6" className="cookies-policy-section-title">
              What are cookies?
            </Typography>
            <Typography>
              A “cookie” is a text file of letters and numbers that websites
              send to a visitor’s computer or other Internet-connected device to
              uniquely identify the visitor’s browser or to store information or
              settings in the browser. A “web beacon,” also known as an Internet
              tag, pixel tag or clear GIF, links web pages to web servers and
              their cookies and may be used to transmit information collected
              through cookies back to a web server. We and our third-party
              service providers may use beacons to help us track response rates,
              identify when webpages are accessed or other types of interactions
              – such as forwarding – and for other purposes listed below.
            </Typography>
            <Typography>
              The term “cookies” includes cookies, tags and pixels. Our websites
              may use cookies to:
            </Typography>
            <Box component="ul" className="cookies-policy-list-item">
              {[
                "Improve the performance of our websites",
                "Enable us to collect information about how users use our websites",
                "Improve users experience on our websites",
                "Deliver relevant online advertising to you both on our websites and elsewhere",
                "Measure the effectiveness of our online advertising and marketing communications",
              ].map((item, index) => (
                <Box
                  component="li"
                  key={index}
                  className="cookies-policy-list-item"
                >
                  <span className="cookies-policy-list-item-dot" >&#8226;</span>{" "}
                  {item}
                </Box>
              ))}
            </Box>
            <Typography>
              This website uses cookies to enhance your browsing experience.
              Among these, cookies categorized as necessary are stored in your
              browser as they are essential for the website’s core
              functionality. We also use third-party cookies to help us
              understand and analyze how you interact with the site. These
              cookies are stored in your browser only with your consent. You
              have the option to opt out of these cookies; however, doing so may
              affect your overall user experience.
            </Typography>
            <Typography>
              If you’d like to change your Cookie preferences then you can use
              this link below to open the cookie preference center.
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default CookiesPolicy;
