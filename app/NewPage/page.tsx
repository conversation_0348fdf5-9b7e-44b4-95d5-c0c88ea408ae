import HeroSection from "@/components/HeroSection/HeroSection";
// import "./page.scss";
import { Box, Divider } from "@mui/material";
import AboutUS from "@/components/AboutUS/About";
import WhatWeDo from "@/components/WhatWeDo/WhatWeDo";
import WhyUs from "@/components/WhyUS/WhyUs";
import IndustriesSlider from "@/components/IndustriesSlider/IndustriesSlider";
import HowWeWork from "@/components/HowWeWork/HowWeWork";
import ConnectForm from "@/components/ConnectForm/ConnectForm";
import OurValues from "@/components/OurValues/OurValues";
import BottomTextBanner from "@/components/BottomTextBanner/BottomTextBanner";
// import "@/app/page.scss";

export default function Home() {
  return (
    <Box className="home-page-container">
      <Box className="home-page-content">
        <Box className="hero-section">
          <HeroSection />
        </Box>
        <Box className="industries-slider">
          <IndustriesSlider />
        </Box>
        <Box className="about-section">
          <AboutUS />
        </Box>
        <Box className="what-section">
          <WhatWeDo />
        </Box>
        <Box className="why-section">
          <WhyUs />
        </Box>
        <Box className="how-section">
          <HowWeWork />
        </Box>
        <Box className="our-section">
          <OurValues />
        </Box>
        <Box className="connect-section">
          <ConnectForm
            heading="Let us know how we can collaborate to create something"
            highlight=" valuable for your business."
          />
        </Box>
      </Box>
    </Box>
  );
}
