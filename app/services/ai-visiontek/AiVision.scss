.ai_vision-page-container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;

  .ai_vision-page-content {
    width: 100vw;
    min-height: 100vh;
    position: relative;

    .ai_vision-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100vw;
      position: relative;
      height: auto;
      // margin-top: 40px;

      .ellipse-container {
        width: 100vw;
        height: auto;
        position: relative;
        z-index: 1;

        img {
          width: 100vw;
          height: auto;
          max-width: 100vw;
          object-fit: contain;
          object-position: top center;
          display: block;
        }
      }

      .hero-content {
        position: absolute;
        left: 0;
        width: 100vw;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        z-index: 2;
        color: #fff;
        text-align: center;
        padding-bottom: 120px;

        .title {
          font-family: Inter !important;
          font-weight: 800;
          font-size: 96px;
          line-height: 100%;
          letter-spacing: 0%;
          color: #97a3aa;
          width: 100vw;
          display: flex;
          justify-content: center;
          align-items: flex-end;
          text-align: center;
          margin: 0;
          padding: 0 16px;

          @media (max-width: 768px) {
            font-size: 48px;
            margin-top: 100px;
          }
        }
      }
    }
    .gen-ai-section {
      position: relative;
      padding: 100px 165px 135px 94px;
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url("../../..//public/assets/images/svg/aivisiontek/aivisiontek.svg")
          no-repeat center center;
        background-size: cover;
        opacity: 0.2; // adjust visibility
        z-index: 0;
      }
      .gen-ai-content {
        position: relative;
        z-index: 1;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        gap: 231px;
        flex-wrap: wrap;
        // align-items: center;

        .gen-ai-left {
          flex: 1;
          max-width: 45%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: flex-start;
          gap: 40px;

          .gen-ai-heading {
            font-size: 36px;
            font-weight: 700;
            color: #ffffff;
            text-align: left;
            line-height: 140%;
            color: #d77d46;
          }

          .gen-ai-image {
            max-width: 300px;
            height: auto;
            object-fit: contain;
          }
        }

        .gen-ai-right {
          flex: 1;
          max-width: 50%;
          display: flex;
          flex-direction: column;
          gap: 24px;

          .gen-ai-paragraph {
            font-family: Montserrat !important;
            font-weight: 500;
            font-style: Medium;
            font-size: 16px;
            letter-spacing: 0%;
            color: #ffffff;
            max-width: 452px;
          }
        }
      }
    }

    .ai-tech-stack-section {
      padding: 63px 95px;
      background-color: #fff;
      .section-title {
        font-family: Inter !important;
        font-size: 32px;
        font-weight: 700;
        color: #021f2e;
        margin-bottom: 63px;
      }
      .section-title-what-we-offer {
        font-family: Inter !important;
        font-weight: 700;
        font-style: Bold;
        font-size: 40px;
        line-height: 100%;
        letter-spacing: 0%;
      }

      .ai-tech-stack-content {
        margin: 0 auto;
        display: flex;
        flex-wrap: wrap;
        gap: 40px;
        justify-content: space-between;

        .ai-tech-left {
          flex: 1;
          max-width: 45%;

          .section-title {
            font-family: Inter;
            font-size: 32px;
            font-weight: 700;
            color: #021f2e;
            margin-bottom: 24px;
          }

          .section-description {
            font-family: Montserrat;
            font-size: 16px;
            line-height: 150%;
            color: #4d4d4d;
          }
        }

        .ai-tech-right-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 24px;
          flex: 1;
          max-width: 50%;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }

          .ai-tech-card {
            padding: 32px 24px;
            border-radius: 12px;
            background-color: #ffffff;
            transition: all 0.3s ease;
            min-height: 160px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            &:hover {
              background-color: #d77d462b;
              transform: translateY(-4px);
            }

            .ai-tech-icon {
              margin-bottom: 20px;
            }

            .ai-tech-title {
              font-family: Montserrat;
              font-size: 16px;
              font-weight: 500;
              color: #d77d46;
            }
          }
        }
      }

      .swiper-section {
        .title {
          display: none;
        }
      }
    }

    .ai-vision-swiper {
      align-items: center;
      padding-top: 67px;
      // padding-left: 97px;
      // padding-right:97px;
    }

    .ai-solutions-section {
      padding: 64px 81px 0 125px;
      background-color: #fff;

      .ai-solutions-container {
        margin: 0 auto;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 40px;

        .ai-solutions-left {
          flex: 1;
          max-width: 50%;

          .ai-solutions-left-content {
            margin-top: 50px;
            .ai-solutions-heading {
              font-family: Inter !important;
              font-weight: 700;
              font-style: Bold;
              font-size: 40px;
              color: #d77d46;
              line-height: 100%;
              letter-spacing: 0%;
            }

            .ai-solutions-link-text {
              margin-top: 24px;

              .ai-solutions-link {
                max-width: 404px;
                font-family: Montserrat !important;
                font-weight: 500;
                font-style: Medium;
                font-size: 15px;
                letter-spacing: 0%;
                vertical-align: middle;
              }
            }
          }
        }

        .ai-solutions-right {
          flex: 1;
          max-width: 50%;
          display: flex;
          justify-content: center;
          align-items: center;

          .solutions-image {
            max-width: 100%;
            height: auto;
          }
        }
      }
    }
    .case-studies-section {
      padding: 0px 139px 100px 139px;
      background-color: #fff;

      .case-studies-title {
        font-family: Inter !important;
        font-weight: 700;
        font-style: Bold;
        font-size: 40px;
        line-height: 100%;
        letter-spacing: 0%;
        margin-bottom: 51px;
      }

      .case-studies-row {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 60px 83px; // vertical x horizontal gap
        justify-content: center;

        @media (max-width: 768px) {
          grid-template-columns: 1fr; // one per row on mobile
        }
      }

      .case-studies-card {
        display: flex;
        flex-direction: column;
        // align-items: center;

        .case-studies-image {
          width: 100%;
          height: 386px;
          object-fit: cover;
          border-radius: 21px;
          transition: transform 0.3s ease;
        }

        .case-studies-content {
          display: flex;
          justify-content: center;
          .case-studies-label {
            justify-content: center;
            font-family: Montserrat !important;
            font-weight: 600;
            font-style: SemiBold;
            font-size: 24px;
            line-height: 30px;
            letter-spacing: 0%;
            vertical-align: bottom;
            color: #021f2e;
            margin-top: 25px;
          }
        }

        &:hover .case-studies-image {
          transform: scale(1.03);
        }
      }
    }
  }
}
