"use client";
import { Box, Typography, List, ListItem, Dialog } from "@mui/material";
import React, { useState } from "react";
import Image from "next/image";
import {
  ar_vr_header_img,
  hero_bg_ellipse,
  explore_img,
  ar_vr_last_img,
  Metaverse_Pop_up,
  mechanical_design_our_process,
  mechanical_design_tool_design_img,
  mechanical_design_star,
  mechanical_design_simulation_img,
  mechanical_design_how_we_work,
  backgroundImg,
} from "@/public/index";
import "./ar-vr-page.scss";
import MechanicalDesign from "@/components/MechanicalDesign/MechanicalDesign";

const ar_vrPage = () => {
  const [open, setOpen] = useState(false);
  const solutions = [
    "Interactive AR components for websites that engage users with product visualizations and informative overlays.",
    "Develop Immersive VR training modules for a more realistic and engaging learning experience.",
    "Custom AR/VR applications tailored to your specific needs and industry.",
    "Customized Training modules for your Employee skill development and learnings.",
  ];
  return (
    <Box className="ar_vr-page-container">
      <Box className="ar_vr-page-content">
        <Box className="ar_vr-page-header">
          <Box className="ellipse-container">
            <Image
              src={hero_bg_ellipse}
              alt="Ellipse"
              className="ellipse-image"
            />
          </Box>

          <Box
            className="hero-content"
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              zIndex: 2,
              color: "#fff",
              textAlign: "center",
            }}
          >
            <Typography className="title" variant="h1">
              Augmented <br /> Reality & <br /> Virtual Reality
            </Typography>
            <Image
              src={ar_vr_header_img}
              alt="Mechanical Design Header Image"
              className="header-image"
            />
          </Box>
        </Box>

        {/* CONTENT */}
        <Box className="content">
          <section className="ar_vr-section">
            <Box className="header-container">
              <Typography variant="h4" className="title">
                Explore AR/VR with Aadvik Labs!
              </Typography>
              <Box className="description-container">
                <Typography variant="body1" className="left-description">
                  Our team is experienced in transformative power of our
                  advanced Virtual Reality (VR), Augmented Reality (AR), and 3D
                  scanning technologies, which bring exceptional value to your
                  testbed and establishment. By leveraging immersive reality, we
                  enhance efficiency, quality, and collaboration across your
                  entire value chain. We can help you to use VR for early
                  engineering design checks and reviews, and AR for
                  comprehensive training and maintenance guidance. Our team can
                  work with you to understand your unique vision and craft a
                  solution that exceeds expectations.
                </Typography>
                <Typography className="right-description">
                  We thrive on innovation by constantly exploring the frontiers
                  of technology. Our focused approach in the dynamic world of
                  Augmented Reality (AR) and Virtual Reality (VR),
                  transformative technologies set to reshape industries. As
                  pioneers in this space, we're excited to deliver
                  next-generation AR/VR websites and software solutions tailored
                  to our clients' evolving needs.
                </Typography>
              </Box>

              <Box className="image-container">
                <Image
                  src={explore_img}
                  alt="Explore Image"
                  className="explore-image"
                />
              </Box>
            </Box>
          </section>

          <Box className="immersive-section">
            <Box className="immersive-container">
              {/* Left content */}
              <Box className="immersive-left">
                <Typography className="immersive-title">
                  What we offer!
                </Typography>
                <Typography className="immersive-paragraph">
                  Our engineers are well experienced in understanding your needs
                  better and figuring out how immersive reality technology can
                  enhance your entire process. With this knowledge, we create
                  custom tools and strategies designed specifically for you. Our
                  expertise helps improve workflows, quality, efficiency, and
                  teamwork across different areas like sales, design, support,
                  and service, unlocking new digital possibilities for your
                  business.
                </Typography>
                <Typography className="immersive-paragraph">
                  Partner with us and unlock the digital potential within your
                  organization. Whether you want to optimize workflows, improve
                  employee skills, or simplify maintenance processes, our
                  comprehensive knowledge of the technology allows us to create
                  unique tools and processes that deliver measurable value in
                  project implementation, service, and maintenance, as well as
                  employee training.
                </Typography>
              </Box>

              {/* Right content */}
              <Box className="immersive-right">
                {solutions.map((desc, index) => (
                  <Box className="solution-item" key={index}>
                    <Image
                      src={mechanical_design_star}
                      alt="AR Icon"
                      width={24}
                      height={24}
                    />
                    <Typography className="solution-text">{desc}</Typography>
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>

          <Box className="simulation-section">
            <Typography className="title">
              Creative Designing & Motion Graphics
            </Typography>

            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
              <Box className="simulation-content">
                <Typography className="simulation-content-descrition">
                  Our Motion Graphics and UI Design Team brings concepts to life
                  through striking visuals and intuitive interfaces. To add life
                  to our designs with AR & VR , we do functional space planning
                  and Immersive Experience Integration to bringing design to
                  live with AR & VR used for architectural walkthroughs, digital
                  showrooms, and client presentations.
                </Typography>
                <Box>
                  <ul className="simulation-list">
                    {[
                      "Virtual tours of smart homes and buildings",
                      "Product explained videos, branding and digital assets",
                      "Virtual tours of smart homes and buildings",
                      "Interactive walkthrough and design previews",
                      "Integration of sustainability & green design principles",
                      "Remote experience access for global clients",
                    ].map((text, index) => (
                      <li className="simulation-list-item" key={index}>
                        <Box className="simulation-list-icon">
                          <Image
                            src={mechanical_design_star}
                            alt="List Icon"
                            width={10}
                            height={10}
                          />
                        </Box>
                        <Box>
                          <Typography className="simulation-list-text">
                            {text}
                          </Typography>
                        </Box>
                      </li>
                    ))}
                  </ul>
                </Box>
              </Box>

              <Box
                className="simulation-image-container"
                onClick={() => setOpen(true)}
              >
                <Image
                  src={ar_vr_last_img}
                  alt="Simulation Image"
                  className="simulation-image"
                />
                <Typography className="text" style={{ cursor: "pointer" }}>
                  Metaverse Experience Center
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="md">
        <Box sx={{ position: "relative" }}>
          <Image
            src={Metaverse_Pop_up}
            alt="Metaverse Pop Up"
            style={{ width: "100%", height: "auto" }}
          />
          <Box
            sx={{
              position: "absolute",
              top: 8,
              right: 8,
              zIndex: 1,
              cursor: "pointer",
              background: "#D77D4636",
              borderRadius: "50%",
              padding: "8px",
            }}
            onClick={() => setOpen(false)}
          >
            <span>
              <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                <path
                  d="M18 6L6 18"
                  stroke="#333"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M6 6L18 18"
                  stroke="#333"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
              </svg>
            </span>
          </Box>
        </Box>
      </Dialog>
    </Box>
  );
};

export default ar_vrPage;
