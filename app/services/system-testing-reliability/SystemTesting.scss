.system-testing-page-container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;

  .system-testing-page-content {
    width: 100vw;
    min-height: 100vh;
    position: relative;

    .system-testing-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      width: 100vw;
      position: relative;

      .ellipse-container {
        width: 100vw;
        height: 100vh;
        position: relative;
        z-index: 1;

        .banner-gradient {
          position: absolute;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          pointer-events: none;
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0.5) 100%
          );
          z-index: 2;
        }

        img {
          width: 100vw;
          height: 100vh;
          object-fit: cover;
          display: block;
        }
      }

      .hero-content {
        position: absolute;
        left: 0;
        width: 100vw;
        height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        z-index: 2;
        color: #fff;
        text-align: center;
        padding-bottom: 148px;

        .title {
          font-family: Inter !important;
          font-weight: 800;
          max-width: 600px;
          font-size: 64px;
          letter-spacing: 0%;
          color: #fff;
          width: 100vw;
          display: flex;
          justify-content: center;
          align-items: flex-end;
          text-align: center;
          margin: 0;
          padding: 0 16px;

          @media (max-width: 768px) {
            font-size: 48px;
            margin-top: 100px;
          }
        }
      }
    }

    /* =========  TESTING & COMPLIANCE SECTION  ========= */
    .testing-compliance-section {
      padding: 107px 102px 150px 92px;
      display: flex;
      flex-wrap: wrap;
      gap: 200px;
      align-items: flex-start;
      background: #fff;

      .testing-left {
        flex: 1;
        max-width: 524px;
      }

      .testing-headline {
        font-family: Montserrat !important;
        font-weight: 400;
        font-style: Regular;
        font-size: 40px;
        letter-spacing: 0%;

        color: #d77d46;
        span {
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
        }
      }

      .testing-right {
        flex: 1;
        max-width: 614px;
        display: flex;
        flex-direction: column;
        gap: 1.25rem;

        .testing-text {
          font-family: Montserrat;
          font-weight: 500;
          font-style: Medium;
          font-size: 20px;
          letter-spacing: 0%;
          color: #021f2e;
        }
      }
    }
    /* =========  OUR SPECIALIZATION SECTION  ========= */
    .specialization-section {
      padding: 0 131px 95px 92px;
      display: flex;
      flex-wrap: wrap;
      gap: 200px;
      align-items: flex-start;
      background: #fff;

      .specialization-heading {
        flex-grow: 1;
        // flex-shrink: 1;
        // flex-basis: 250px;
        max-width: 524px;
        font-family: Inter !important;
        font-weight: 700;
        font-style: Bold;
        font-size: 40px;
        letter-spacing: 0%;

        color: #021f2e;
      }

      .specialization-body {
        flex: 1 1 380px;
        max-width: 615px;
        font-family: Montserrat !important;
        font-weight: 500;
        font-style: Medium;
        font-size: 20px;
        letter-spacing: 0%;
        color: #021f2e;
      }
    }
    .what-we-offer-section {
      background: #fff;
      //   padding: 80px 0px;
      padding: 0 95px 80px 95px;
    }
    .why-choose-us {
      padding: 77px 0 0 0;
      img {
        width: 100%;
        height: 656px !important;
        height: auto;
      }
    }
    /* ========= TOOLS & PLATFORM SECTION ========= */
    .tools-platform-section {
      padding: 93px 95px 93px 95px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 40px;
      background: #fff;

      .tools-section-title {
        font-family: Inter !important;
        font-weight: 700;
        font-size: 40px;
        color: #021f2e;
        text-align: center;
        margin-bottom: 20px;
      }

      .tools-table-container {
        width: 100%;
        max-width: 1000px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .tools-table {
        width: 100%;
        display: flex;
        flex-direction: column;
        background: #fff;

        .table-row {
          display: flex;
          width: 100%;
          border-bottom: 1px solid #8B8B8B;

          &:last-child {
            border-bottom: none;
          }

          &.header-row {
            background: #D77D46;

            .header-cell {
              color: #fff;
              font-weight: 700;
              font-size: 18px;
              padding: 20px 38px;
            }
          }

          .table-cell {
            flex: 1;
            padding: 16px 38px;
            font-family: Poppins !important;
            font-weight: 400;
            font-size: 16px;
            color: #021f2e;
            // border-right: 1px solid #e0e0e0;
            display: flex;
            align-items: center;

            &:last-child {
              border-right: none;
            }
          }
        }
      }

      .tools-caption {
        max-width: 740px;
        font-family: Poppins !important;
        font-weight: 400;
        font-style: italic;
        font-size: 20px;
        letter-spacing: 0%;
        text-align: center;
        vertical-align: middle;
        color: #666;
        margin-top: 20px;
      }

      @media (max-width: 768px) {
        padding: 60px 24px;

        .tools-table-container {
          overflow-x: auto;
        }

        .tools-table {
          min-width: 600px;
        }

        .table-row .table-cell {
          padding: 12px 16px;
          font-size: 14px;
        }

        .tools-section-title {
          font-size: 32px;
        }

        .tools-caption {
          font-size: 16px;
        }
      }
    }
  }
}
