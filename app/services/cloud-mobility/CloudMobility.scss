.cloud-mobility-page-container {
  .cloud-mobility-page-content {
    background-color: #ffffff;

    .cloud-mobility-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-height: 100vh;
      height: 100%;
      width: 100%;

      .cloud-mobility-home-container {
        position: relative;
        width: 100%;
        min-height: 100vh;
        height: 100%;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .banner-text {
          position: absolute;
          bottom: 60px;
          color: #a4a9ac;
          left: 50%;
          transform: translateX(-50%);
          font-family: Inter;
          font-weight: 800;
          font-style: Extra Bold;
          font-size: 64px;
          max-width: 90%;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: center;
          white-space: nowrap;
          vertical-align: middle;
        }
      }
    }
    .cloud-innovation-section {
      background: #fff;
      padding: 80px 0px;

      .cloud-innovation-content {
        // max-width: 1600px;
        padding: 0 94px 99px 94px;
        margin: 0 auto;

        .innovation-grid {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          flex-wrap: wrap;
          gap: 40px;

          .left-text,
          .right-text {
            flex: 1;
            max-width: 45%;
            font-family: Montserrat !important;
            font-weight: 500;
            font-style: Medium;
            font-size: 16px;
            line-height: 100%;
            letter-spacing: 0%;

            .highlighted-heading {
              font-family: Montserrat !important;
              font-weight: 500;
              color: #d77d46;
              font-style: Medium;
              font-size: 40px;
              line-height: 120%;
              letter-spacing: 0%;
              margin-bottom: 53px;

              .highlight {
                font-family: Montserrat !important;
                font-weight: 700;
                font-style: Bold;
                font-size: 40px;
                line-height: 100%;
                letter-spacing: 0%;
              }
            }
          }

          .left-description {
            font-family: Montserrat;
            color: #021f2e;
            font-weight: 500;
            font-style: Medium;
            font-size: 16px;
            line-height: 160%;
            letter-spacing: 0%;
          }

          .right-text-align {
            margin-top: 54px;
            .right-description {
              margin-top: 53px;
              color: #021f2e;
              font-family: Montserrat;
              font-weight: 500;
              font-style: Medium;
              font-size: 16px;
              line-height: 160%;
            }

            .right-description-two {
              color: #021f2e;
              font-family: Montserrat;
              font-weight: 500;
              font-style: Medium;
              font-size: 16px;
              line-height: 160%;
            }

            .center-icon {
              display: flex;
              justify-content: center;
              align-items: flex-start;
              width: 10%;
              min-width: 60px;
              padding-top: 8px;

              img {
                width: 72px;
                height: 72px;
              }
            }
          }
        }
      }
    }

    .what-we-offer-section {
      background: #fff;
      //   padding: 80px 0px;
      padding: 0 95px 80px 95px;
      .what-we-offer-title {
        font-family: Inter !important;
        font-weight: 700;
        font-style: Bold;
        font-size: 40px;
        letter-spacing: 0%;
        margin-bottom: 49px;
      }
    }
    .expertise-section {
      padding: 0 92px 80px 92px;
      background-color: #fff;

      .expertise-content {
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        gap: 40px;
        flex-wrap: wrap;
      }

      .expertise-left {
        flex: 1;
        max-width: 50%;
        font-family: Montserrat;

        .expertise-heading {
          font-family: Inter !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          line-height: 100%;
          letter-spacing: 0%;
          margin-bottom: 100px;
        }

        .expertise-description {
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 16px;
          line-height: 140%;
          letter-spacing: 0%;
          max-width: 453px;
          margin-bottom: 44px;
        }

        .expertise-point {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          margin-bottom: 16px;
          .expertise-point-text {
            font-family: Montserrat !important;
            font-weight: 500;
            font-style: Medium;
            font-size: 16px;
            letter-spacing: 0%;
            color: #021f2e;
            max-width: 413px;
          }

          img {
            width: 20px;
            height: 20px;
            margin-top: 4px;
          }

          p,
          span,
          strong {
            font-size: 16px;
            line-height: 160%;
            color: #1e1e1e;
          }
        }
      }

      .expertise-right {
        flex: 1;
        max-width: 45%;

        .expertise-image {
          width: 100%;
          height: auto;
          object-fit: contain;
        }
      }
    }
    .devops-section {
      min-height: 538px;
      background-color: #021f2e;
      padding: 105px 300px 64px 94px;

      .devops-container {
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .devops-heading {
        font-family: Montserrat !important;
        font-weight: 500;
        font-style: Medium;
        font-size: 40px;
        letter-spacing: 0%;
        color: #fff;

        .highlight {
          color: #d77d46;
          font-weight: 700;
          font-style: Bold;
        }
      }
      .devops-description-block {
        display: flex;
        justify-content: flex-end;
        .devops-description {
          justify-content: flex-end;
          font-family: Montserrat !important;
          font-weight: 400;
          font-size: 16px;
          line-height: 160%;
          color: #ffffff;
          // max-width: 50%;
          max-width: 452px;
        }
      }
    }

    .devops-consulting {
     padding: 99px 0 125px 0;
      // img {
      //   min-height: 473px;
      // }
      .devops-consulting-title {
        font-family: Inter !important;
        font-weight: 700;
        font-style: Bold;
        font-size: 40px;
        line-height: 100%;
        letter-spacing: 0%;
        text-align: center;
      }
      .devops-consulting-container {
        display: flex;
        justify-content: center;
      }
    }
    .devops-benefits-section {
      background-color: #fff;
      padding: 0px 40px 80px 130px;

      .devops-benefits-content {
        margin: 0 auto;
        font-family: Montserrat;
        text-align: center;

        .benefits-heading-block {
          padding-left: 51px;
          .benefits-heading {
            font-family: Inter !important;
            font-weight: 700;
            font-style: Bold;
            font-size: 40px;
            line-height: 100%;
            letter-spacing: 0%;
            text-align: center;

            margin-bottom: 40px;
          }
        }

        .benefits-list {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 20px;

          .benefit-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            font-family: Montserrat !important;
            font-weight: 500;
            font-style: Medium;
            font-size: 16px;
            line-height: 20px;
            letter-spacing: 0%;

            img {
              width: 20px;
              height: 20px;
              margin-top: 4px;
            }
          }
        }
      }
    }
  }
}
