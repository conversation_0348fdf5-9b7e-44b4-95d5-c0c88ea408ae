import { Box, Typography, Divider } from "@mui/material";
import Image from "next/image";
import MechanicalTestingSwiper from "./MechanicalTestingSwiper";
import {
  mechanical_design_header_img,
  hero_bg_ellipse,
  mechanical_design_our_process,
  mechanical_design_tool_design_img,
  mechanical_design_star,
  mechanical_design_simulation_img,
  mechanical_design_how_we_work,
} from "@/public/index";
import "./mechanical-page.scss";
import MechanicalDesign from "@/components/MechanicalDesign/MechanicalDesign";

const MechanicalPage = () => {
  const data = [
    {
      number: "01",
      title: "Thermal Testing",
      description:
        "Our engineers involves in testing system level performance and  reliability  under  extreme temperature conditions, including exposure to high and low temperatures and thermal cycling. This ensures the system remains operational across a wide temperature range.",
    },
    {
      number: "02",
      title: "Vibration & Shock Testing",
      description:
        "Simulates the mechanical vibrations  and  impact  due  to  shock  for the unit under test. The goal is to verify that component  functionality under sustained or random vibration stress and continued functionality after experiencing physical shocks.",
    },
    {
      number: "03",
      title: "Humidity Testing",
      description:
        "Evaluates   a  system’s  durability  when  exposed  to  high  humidity  levels  and  condensation .  This  test  identifies  potential issues such as corrosion, material degradation, or failure of sensitive electronic components",
    },
    {
      number: "04",
      title: "Corrosion Test",
      description:
        "Corrosion Test is a crucial system resistance test for corrosive  environments operations particularly  useful  for assessing the longevity of coatings, materials, and finishes in marine or industrial settings. By  exposing the unit into  a  controlled  salt  fog in a specialized chamber. ",
    },
  ];

  return (
    <Box className="mechanical-page-container">
      <Box className="mechanical-page-content">
        <Box className="mechanical-page-header">
          <Box className="ellipse-container">
            <Image
              src={hero_bg_ellipse}
              alt="Ellipse"
              className="ellipse-image"
            />
          </Box>

          <Box
            className="hero-content"
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 2,
              color: "#fff",
              textAlign: "center",
            }}
          >
            <Typography className="title" variant="h1">
              Mechanical Design
            </Typography>
            <Image
              src={mechanical_design_header_img}
              alt="Mechanical Design Header Image"
              className="header-image"
            />
          </Box>
        </Box>

        {/* CONTENT */}
        <Box className="content">
          <MechanicalDesign />

          <Box className="our-process-section">
            <Typography className="title">Our Process</Typography>
            <Image
              src={mechanical_design_our_process}
              alt="Our Process"
              className="our-process-image"
            />
          </Box>

          <Box className="tool-design-section">
            <Box className="left-section">
              <Typography variant="h2" className="title">
                Tool Design and Development
              </Typography>
              <Typography variant="body1" className="left-sub-title">
                Tool Design and Development is indeed a true art of engineering,
                where technical expertise and creativity merge together to
                create solutions that either improve productivity or enhance
                user experience. We provide design consultancy and development
                support to create a right space for your product with right form
                factor, functionality, and aesthetics.{" "}
              </Typography>

              <Image
                src={mechanical_design_tool_design_img}
                alt="Tool Design Image"
                className="tool-design-image"
              />
            </Box>

            <Box className="right-section">
              <Box className="description-box">
                <Typography>
                  Our expertise spans the entire product development life cycle,
                  from initial concept and design to manufacturing. By working
                  closely with our long-term manufacturing partners, we can
                  produce array of tools and parts to meet your diverse industry
                  needs. Our specialization are : -
                </Typography>
              </Box>

              <Box className="cards">
                <Box className="card">
                  <Image
                    src={mechanical_design_star}
                    alt="Star Icon"
                    className="star-icon"
                  />

                  <Typography variant="h4" className="title">
                    Plastic Injection Molding
                  </Typography>

                  <Typography variant="body1" className="description">
                    Tool designing for plastic injection molding involves
                    creating molds with single or multiple core-cavity
                    configurations to produce high-quality plastic parts.
                  </Typography>
                </Box>

                <Box className="card">
                  <Image
                    src={mechanical_design_star}
                    alt="Star Icon"
                    className="star-icon"
                  />

                  <Typography variant="h4" className="title">
                    Plastic Injection Molding
                  </Typography>

                  <Typography variant="body1" className="description">
                    Tool designing for plastic injection molding involves
                    creating molds with single or multiple core-cavity
                    configurations to produce high-quality plastic parts.
                  </Typography>
                </Box>

                <Box className="card">
                  <Image
                    src={mechanical_design_star}
                    alt="Star Icon"
                    className="star-icon"
                  />

                  <Typography variant="h4" className="title">
                    Plastic Injection Molding
                  </Typography>

                  <Typography variant="body1" className="description">
                    Tool designing for plastic injection molding involves
                    creating molds with single or multiple core-cavity
                    configurations to produce high-quality plastic parts.
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>

          <Box className="simulation-section">
            <Typography className="title">Simulation Analysis</Typography>

            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
              <Box className="simulation-content">
                <Typography>
                  Aadvik TekLabs team’s focus is on providing end-to-end
                  solutions, ensuring that the enclosures we design not only
                  protect valuable electronics but also support long-term
                  reliability and efficiency in a variety of industries.always
                  aim to add an extra edge to our thought process, turning
                  innovative ideas into competitive products. We create designs
                  to address futuristic challenges by thinking beyond convention
                  and embracing cutting-edge solutions.
                </Typography>
                <Typography>
                  Our deep expertise in CAE (Computer-Aided Engineering), FEA
                  (Finite Element Analysis), CFD (Computational Fluid Dynamics),
                  and numerical analysis gives you a distinct competitive edge
                  in the engineering world.
                </Typography>{" "}
                <Typography>
                  By applying these advanced methods, we ensure that every
                  design is optimized for maximum performance, efficiency, and
                  innovation.
                </Typography>
              </Box>

              <Box>
                <Image
                  src={mechanical_design_simulation_img}
                  alt="Simulation Image"
                  className="simulation-image"
                />
              </Box>
            </Box>
          </Box>

          <Box className="mechanical-testing-section">
            <MechanicalTestingSwiper data={data} />
          </Box>

          <Box className="how-we-work-section">
            <Box className="left-section">
              <Typography variant="h2" className="title">
                How we work ?
              </Typography>

              <Typography variant="body1" className="description">
                Aadvik TekLabs engineering team objective is to develop a robust
                system capable of build and deliver mechanical designs which can
                maintain reliable performance under extreme conditions.
              </Typography>
              <Typography variant="body1" className="description">
                We deliver precise, optimized solutions that push the boundaries
                of innovation and performance ensuring the product functionality
                made to be used for outdoor or industrial environment despite
                exposure to dust, water, and other potential hazards. 
              </Typography>
            </Box>

            <Box className="right-section">
              <Image
                src={mechanical_design_how_we_work}
                alt="How We Work Image"
                className="how-we-work-image"
              />
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default MechanicalPage;
