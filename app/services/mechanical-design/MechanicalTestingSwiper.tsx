"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import type { Swiper as SwiperType } from "swiper";
import { Autoplay, FreeMode } from "swiper/modules";
import "swiper/css";
import {
  Box,
  Divider,
  Typography,
  List,
  ListItem,
  ListItemText,
} from "@mui/material";
import "./MechanicalTestingSwiper.scss";
import { useRef } from "react";

interface TestCard {
  number: string;
  title: string;
  description: string | string[];
}

interface MechanicalTestingSwiperProps {
  data: TestCard[];
  hoverColor?: string;
  hoverTextColor?: string;
  hoverTextColorDescription?: string;
}

const MechanicalTestingSwiper = ({
  data,
  hoverColor = "#d77d46",
  hoverTextColor,
  hoverTextColorDescription,
}: MechanicalTestingSwiperProps) => {
  const swiperRef = useRef<SwiperType | null>(null);

  // Duplicate data array to ensure enough slides for looping
  const loopedData =
    data.length < 4 ? [...data, ...data, ...data, ...data] : data;

  const handleMouseEnter = () => {
    if (swiperRef.current && swiperRef.current.autoplay) {
      swiperRef.current.autoplay.pause(); // Pause for smooth stop on hover
    }
  };

  const handleMouseLeave = () => {
    if (swiperRef.current && swiperRef.current.autoplay) {
      swiperRef.current.autoplay.resume(); // Resume autoplay smoothly
    }
  };

  return (
    <Box className="mechanical-testing-section">
      <Swiper
        modules={[Autoplay, FreeMode]}
        spaceBetween={30}
        slidesPerView={1}
        loop={true}
        speed={8000}
        autoplay={{
          delay: 1,
          disableOnInteraction: false,
          pauseOnMouseEnter: false,
        }}
        freeMode={{ enabled: true, momentum: false, momentumRatio: 1 }}
        allowTouchMove={false}
        breakpoints={{
          600: { slidesPerView: 3, spaceBetween: 30 },
          1024: { slidesPerView: 2.2, spaceBetween: 30 },
          1200: { slidesPerView: 2.8, spaceBetween: 30 },
          1300: { slidesPerView: 3, spaceBetween: 30 },
          1400: { slidesPerView: 3.2, spaceBetween: 30 },
          1500: { slidesPerView: 3.4, spaceBetween: 30 },
          1600: { slidesPerView: 3, spaceBetween: 30 },
          1700: { slidesPerView: 4, spaceBetween: 30 },
          1800: { slidesPerView: 4, spaceBetween: 30 },
          1900: { slidesPerView: 4.2, spaceBetween: 30 },
        }}
        style={{ padding: "0 20px 40px 20px" }}
        onSwiper={(swiper) => (swiperRef.current = swiper)}
      >
        {loopedData.map((item, index) => (
          <SwiperSlide key={index}>
            <div
              className="card"
              style={{
                ["--card-hover-bg" as any]: hoverColor,
                ["--card-hover-text" as any]: hoverTextColor,
                ["--card-hover-text-description" as any]:
                  hoverTextColorDescription,
              }}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              <div className="number">{item.number}</div>
              <div className="title">
                <span dangerouslySetInnerHTML={{ __html: item.title }} />
                <Divider
                  className="divider"
                  sx={{ borderColor: "#fff", borderWidth: 1, margin: "10px 0" }}
                />
              </div>
              <div className="text">
                {Array.isArray(item.description) ? (
                  <List
                    sx={{
                      padding: 0,
                      listStyleType: "disc",
                      color: "#fff",
                      "& .MuiListItem-root": {
                        display: "list-item",
                        padding: 0,
                        // marginBottom: "8px",
                      },
                      "& .MuiListItemText-primary": {
                        color: "#fff",
                        fontSize: "15px",
                      },
                      "& .MuiListItem-root::marker": {
                        color: "#fff",
                        fontSize: "15px",
                      },
                    }}
                  >
                    {item.description.map((desc, i) => (
                      <ListItem key={i}>
                        <ListItemText primary={desc} />
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Typography className="description">
                    {item.description}
                  </Typography>
                )}
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </Box>
  );
};

export default MechanicalTestingSwiper;
