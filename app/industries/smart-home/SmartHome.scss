.smart-home-page-container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;

  .smart-home-page-content {
    width: 100vw;
    min-height: 100vh;
    position: relative;

    .smart-home-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      width: 100vw;
      position: relative;

      .ellipse-container {
        width: 100vw;
        height: 100vh;
        position: relative;
        z-index: 1;

        .banner-gradient {
          position: absolute;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          pointer-events: none;
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0.5) 100%
          );
          z-index: 2;
        }

        img {
          width: 100vw;
          height: 100vh;
          object-fit: cover;
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }
      }

      .hero-content {
        position: absolute;
        top: 35%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 999px;
        height: 124px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3;
        color: #fff;
        text-align: center;
        padding-bottom: 0;

        .title {
          font-family: Inter !important;
          font-weight: 800;
          font-style: Extra Bold;
          font-size: 64px;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
          color: #eaecee;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .smart-living-section {
      background-color: #ffffff;
      padding: 95px 80px 120px 95px;

      .smart-living-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        flex-wrap: wrap;
        gap: 40px;
      }

      .smart-living-left {
        flex: 1;

        .smart-living-heading {
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          color: rgba(215, 125, 70, 1);
        }
      }

      .smart-living-right {
        flex: 1;
        max-width: 429px;

        .smart-living-description {
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 16px;
          letter-spacing: 0%;

          color: rgba(2, 31, 46, 1);
        }
      }
    }

    .smart-home-page-component-section {
      background: #fff;
      //   padding: 80px 0px;
      padding: 0 95px 80px 95px;
    }
    .smart-home-page-oem {
      padding: 54px 83px;
      background-color: #fff;

      .oem-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 40px;
      }

      .oem-image {
        flex: 1;
        // max-width: 493px;
        // max-height: 329px;

        img {
          width: 100%;
          height: auto;
          display: block;
        }
      }

      .oem-content {
        flex: 1;
        max-width: 676px;
      }

      .oem-heading {
        font-family: Inter !important;
        font-weight: 700;
        font-size: 40px;
        margin-bottom: 33px;
        color: #021f2e;
      }

      .oem-description {
        font-family: Montserrat !important;
        font-weight: 500;
        font-size: 16px;
        color: #021f2e;
        max-width: 533px;
      }
    }

    .smart-appliance-map-section {
      width: 100%;
      padding: 60px 0;
      background-color: #032333; // Matches your visual theme
      display: flex;
      justify-content: center;
      align-items: center;

      .appliance-map-image {
        height: auto;
        object-fit: contain;
      }
    }

    .support-section {
      padding: 60px 0px;
      text-align: center;
      background-color: #ffffff;

      .support-heading {
        font-family: Inter !important;
        font-weight: 700;
        font-style: Bold;
        font-size: 40px;
        text-align: center;
        margin-bottom: 33px;
      }

      .support-divider-container {
        margin-right: 82px;
        margin-left: 82px;
        margin-bottom: 33px;
        .support-divider {
          width: 100%;
          border: 1px solid #021f2e;
        }
      }
      .support-description-container {
        max-width: 706px;
        display: flex;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 69px;

        .support-description {
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 16px;
          letter-spacing: 0%;
          text-align: center;
          color: #021f2e;
        }
      }

      .support-columns {
        display: flex;
        justify-content: center;
        gap: 250px;
        flex-wrap: wrap;

        .support-column {
          flex: 1 1 30px;
          max-width: 400px;
          text-align: left;

          .column-title {
            font-family: Inter;
            font-weight: 700;
            font-style: Bold;
            font-size: 24px;
            line-height: 100%;
            letter-spacing: 0%;
            margin-bottom: 38px;
          }

          ul {
            list-style: none;
            padding: 0;

            li {
              font-family: Inter;
              font-weight: 400;
              font-style: Regular;
              font-size: 20px;
              line-height: 50px;
              letter-spacing: 0%;
            }
          }
        }

        .column-divider {
          border: 0.62px #3e88b1 solid !important;
        }
      }
      .support-divider {
        margin-top: 33px;
        border: 0.62px #3e88b1 solid !important;
      }
    }

    .engineering-section {
      background-color: #ffffff;
      padding: 0 133px 151px 83px;
      .engineering-section-title-container {
        max-width: 1028px;
        align-items: center;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 116px;
        .section-title {
          font-family: Inter !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          text-align: center;
        }
      }

      .engineering-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 40px;

        @media (min-width: 960px) {
          flex-direction: row;
          align-items: flex-start;
          justify-content: space-between;
        }

        .engineering-text {
          max-width: 489px;
          font-size: 16px;
          line-height: 1.6;
          color: #333;

          p + p {
            margin-top: 20px;
          }
        }

        .engineering-image {
          flex-shrink: 0;

          .responsive-img {
            max-width: 100%;
            height: auto;
          }
        }
      }
    }
  }
}
