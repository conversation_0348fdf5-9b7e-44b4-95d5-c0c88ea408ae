"use state";
import { Box, Divider, Typography } from "@mui/material";
import Image from "next/image";
import "./SmartHome.scss";
import {
  IntroBanner,
  OemImage,
  ProductEngineeringImage,
  SmartHomeBanner,
  SmartHomeBanner2,
  SmartHomeIntroImage,
} from "@/public";
import WhatWeOffer from "@/components/Whatweoffer/WhatWeOffer";

const SmartHomePage = () => {
  const services = [
    {
      title:
        "Connect and manage smart devices via industry standard protocols like Zigbee, Z-Wave, or Wi-Fi.",
    },
    {
      title:
        "Proven system design knowledge of Micro system, sensors, and actuators for device control.",
    },
    {
      title:
        "Mobile & Web App Development - Creating user-friendly interfaces for control and monitoring.",
    },
    {
      title:
        "Implementing the encryption, authentication, and data protection algorithms to secure the system.",
    },
    {
      title:
        "Designing intelligent routines and learning systems for adaptive home behavior using GenAI.",
    },
    {
      title:
        "Manage, storage, process the data by leveraging the power of edge computing and remote access via cloud platforms.",
    },
  ];
  return (
    <Box className="smart-home-page-container">
      <Box className="smart-home-page-content">
        <Box className="smart-home-page-header">
          <Box className="ellipse-container">
            <Image
              src={SmartHomeBanner}
              alt="Ellipse"
              className="ellipse-image"
            />
            {/* Gradient overlay goes here */}
            <Box className="banner-gradient" />
          </Box>

          <Box className="hero-content">
            <Typography className="title">Smart Home & Buildings</Typography>
          </Box>
        </Box>

        <Box className="smart-living-section">
          <Box className="smart-living-container">
            <Box className="smart-living-left">
              <Typography className="smart-living-heading">
                Building Intelligent <br />
                Spaces for a Smart Living
              </Typography>
            </Box>
            <Box className="smart-living-right">
              <Typography className="smart-living-description">
                Aadvik Teklabs, a leading engineering service provider in the
                smart home and building domain, delivers customized home
                automation solutions with a strong commitment to end-to-end
                automation and security, driving the development of futuristic
                living spaces.
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Componenet Section */}
        <Box className="smart-home-page-component-section">
          <WhatWeOffer services={services} />
        </Box>
        {/* Oem Section */}

        <Box className="smart-home-page-oem">
          <Box className="oem-container">
            <Box className="oem-image">
              <Image src={OemImage} alt="OEM Illustration" />
            </Box>
            <Box className="oem-content">
              <Typography variant="h4" className="oem-heading">
                Enabling OEMs with Intelligent Home Automation System Design
              </Typography>
              <Typography variant="body1" className="oem-description">
                The living world is rapidly evolving with demand for efficient,
                connected and trustworthy systems. Aadvik Team work closely with
                OEM partners in Smart Lighting and Home Automation to build and
                deliver robust and with energy savings. We at AadvikLabs enables
                the intelligence in Legacy products to make them smart and
                efficient to transforms the traditional home into a more secure,
                user-friendly and all-time available by leveraging technology to
                streamline daily tasks and routines.
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* map-section */}
        <Box className="smart-appliance-map-section">
          <Image
            src={IntroBanner}
            alt="Smart Appliance Visual Map"
            className="appliance-map-image"
            priority
          />
        </Box>

        {/* How we support */}
        <Box className="support-section">
          <Typography variant="h4" className="support-heading">
            How we Support ?
          </Typography>

          <Box className="support-divider-container">
            <Divider className="support-divider" />
          </Box>
          <Box className="support-description-container">
            <Typography className="support-description">
              We collaborate with OEMs to develop high-performance smart home
              and lighting systems that integrate energy efficiency with
              advanced automation features. Our team is having rich experience
              in building intelligent home automation solutions which can
              deliver seamless control, enhanced security with optimized energy
              usage, empowering smarter and more connected living environments.
            </Typography>
          </Box>

          <Box className="support-columns">
            <Box className="support-column">
              <Typography variant="h6" className="column-title">
                Luminescence Solutions
              </Typography>
              <ul>
                <li>✔ Lighting Systems for Appliances</li>
                <li>✔ Smart Street Lighting</li>
                <li>✔ Lighting Controllers, Sensors & Drivers</li>
                <li>✔ Signage Lighting Design</li>
                <li>✔ Horticultural Lighting</li>
              </ul>
            </Box>

            <Divider
              orientation="vertical"
              flexItem
              className="column-divider"
            />

            <Box className="support-column">
              <Typography variant="h6" className="column-title">
                Home Automation System
              </Typography>
              <ul>
                <li>✔ Voice & Remote Control</li>
                <li>✔ HVAC Control Systems</li>
                <li>✔ Home Security & Access</li>
                <li>✔ Shades, Doors & Gates</li>
                <li>✔ Alarms & Safety Systems</li>
              </ul>
            </Box>
          </Box>
          <Box className="support-divider" />
        </Box>

        {/* Product Engineering section */}
        <Box className="engineering-section">
          <Box className="engineering-section-title-container">
            <Typography variant="h4" className="section-title">
              Product Engineering Services for Lighting, Home Automation &
              Consumer Electronics
            </Typography>
          </Box>

          <Box className="engineering-content">
            <Box className="engineering-text">
              <Typography>
                The modern technological development requires innovative
                solutions for lighting control systems combined with automated
                home devices and consumer electronic products. Modern businesses
                create aware products which use minimal energy and match modern
                living styles. Market-leading organizations select professional
                product engineering experts to translate their provisional ideas
                into operational realities in this evolving market environment.
              </Typography>

              <Typography>
                Aadvik Teklabs delivers complete product engineering solutions
                for Lighting and Home Automation and consumer electronics
                products according to sector demands. Aadvik Teklabs integrates
                experts from design and engineering and technology fields to
                create solutions which support functionality while creating
                consumer-brand connection points.
              </Typography>
            </Box>

            <Box className="engineering-image">
              <Image
                src={ProductEngineeringImage}
                alt="Product Engineering Visual"
                width={550}
                height={300}
                className="responsive-img"
              />
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default SmartHomePage;
