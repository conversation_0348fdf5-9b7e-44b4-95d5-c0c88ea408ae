"use state";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import "./HeatingVentilation.scss";
import {
  EmpoeringAll,
  EmpoweringSmarter,
  HeatingVentilationBanner,
} from "@/public";
import MechanicalTestingSwiper from "../../services/mechanical-design/MechanicalTestingSwiper";

const HeatingVentilationPage = () => {
  const data = [
    {
      number: "01",
      title: "Environmental Parameter Controls (EPC)",
      description: [
        "Control Systems Design for sensors & Actuators & other Env Sensors",
        "Integrating Env Sensors like Temperature, Humidity, photo detectors, Motion Sensors",
        "User Interface & Interaction System design",
      ],
    },
    {
      number: "02",
      title: "Wireless Connectivity Ennoblement",
      description: [
        "Communication channel establishment like WI-Fi, BLE, Zigbee, and  thread",
        "Communication Protocol development",
        "Communication Module development",
        "Signal strength & Range testing",
      ],
    },
    {
      number: "03",
      title: "Network Stack Integration",
      description: [
        "UI/UX design & development for HVAC system controls and maintenance",
        "Graphical Visualization & Device Controls",
        "Push Notifications & Alerts Systems",
        "Zones & Rooms design & Control",
        "App development for Fault Diagnostics",
      ],
    },
    {
      number: "04",
      title: "Predictive Maintenance & Remote Monitoring",
      description: [
        "System Energy Efficiency Calculations",
        "Data Collection and Analytics",
        "Scalability & Interoperability Testing",
        "Track maintenance schedules",
        "Optimized Inventory Management",
        "App development for field debugging and Maintenance",
      ],
    },
    {
      number: "05",
      title: "Integration with BMS & Home Automation",
      description: [
        "Automates centralize control design",
        "System connectivity with BACnet Protocols",
        "Connectivity with other building systems like security, fire alarms",
        "Energy management, and other critical building functions",
      ],
    },
    {
      number: "06",
      title: "Firmware Development & OTA Support",
      description: [
        "Firmware Development for control system operations and FOTA Support",
        "Communication Protocol development & Integration support",
        "Enabling Smart Features for EPC Controls",
      ],
    },
  ];
  return (
    <Box className="heating-ventilation-page-container">
      <Box className="heating-ventilation-page-content">
        <Box className="heating-ventilation-page-header">
          <Box className="ellipse-container">
            <Image
              src={HeatingVentilationBanner}
              alt="Ellipse"
              className="ellipse-image"
            />
            {/* Gradient overlay goes here */}
            <Box className="banner-gradient" />
          </Box>

          <Box className="hero-content">
            <Typography className="title" variant="h1">
              Heating & Ventilation
            </Typography>
          </Box>
        </Box>
        <Box className="climate-controls-section">
          <Box className="headline-container">
            <Typography component="h2" className="headline">
              Revolutionizing Climate Controls with Smarter, Connected, and
              Sustainable Solutions for a Greener Future
            </Typography>
          </Box>

          <Box className="description-container">
            <Typography className="description" gutterBottom>
              As the world recovers from the pandemic, the urgency to address
              climate change is clearer than ever. Aadvik TekLabs partners with
              HVAC and industrial machine manufacturers to create
              energy‑efficient, all‑weather responsive systems.
            </Typography>

            <Typography className="description">
              By integrating advanced communication technologies, we make HVAC
              solutions smarter, more connected, and eco‑conscious—ensuring
              comfort without compromising the planet.
            </Typography>
          </Box>
        </Box>

        {/* Swiper section */}
        <Box className="swiper-section-heating-ventilation">
          <MechanicalTestingSwiper data={data} />
        </Box>
        <Box className="hvac-solutions-section">
          {/* Left Text Section */}
          <Box className="hvac-text">
            <Typography className="hvac-title" component="h2">
              Empowering Smarter, <br />
              Greener HVAC Solutions
            </Typography>
            <Typography className="hvac-description" gutterBottom>
              Whether it's a compact residential split AC or a large-scale
              commercial HVAC system, we equip our partners with cutting-edge
              tools and technologies to provide forward-thinking HVAC solutions.
            </Typography>
            <Typography className="hvac-description">
              Our dedicated engineering team focuses on delivering systems that
              are more responsive, connected, and environmentally
              responsible—ensuring optimal indoor comfort while significantly
              reducing energy consumption.
            </Typography>
          </Box>

          {/* Right Image Section */}
          <Box className="hvac-image">
            <Image
              src={EmpoweringSmarter}
              alt="HVAC Solutions"
              className="responsive-image"
            />
          </Box>
        </Box>
        <Box className="empowering-all-conatiner">
          <Image
            src={EmpoeringAll}
            alt="EmpoeringAll"
            className="full-width-image"
            layout="responsive"
            sizes="100vw"
          />
        </Box>
      </Box>
    </Box>
  );
};

export default HeatingVentilationPage;
