.heating-ventilation-page-container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;

  .heating-ventilation-page-content {
    width: 100vw;
    min-height: 100vh;
    position: relative;

    .heating-ventilation-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      width: 100vw;
      position: relative;

      .ellipse-container {
        width: 100vw;
        height: 100vh;
        position: relative;
        z-index: 1;

        .banner-gradient {
          position: absolute;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          pointer-events: none;
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0.5) 100%
          );
          z-index: 2;
        }

        img {
          width: 100vw;
          height: 100vh;
          object-fit: cover;
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }
      }

      .hero-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 999px;
        height: 124px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3;
        color: #fff;
        text-align: center;
        padding-bottom: 0;

        .title {
          font-family: Inter !important;
          font-weight: 800;
          font-style: Extra Bold;
          font-size: 64px;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
          width: 100%;
          height: 100%;
          margin: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #d4d8da;
        }
      }
    }
    .climate-controls-section {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 40px;
      padding: 110px 95px 155px 92px;
      box-sizing: border-box;
      background: #ffffff;

      .headline-container {
        flex: 1 1 45%;
        .headline {
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          color: #d77d46;
          max-width: 557px;
        }
      }

      .description-container {
        flex: 1 1 45%;
        .description {
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 20px;
          letter-spacing: 0%;
          max-width: 515px;
          margin-bottom: 45px;
        }
      }

      @media (max-width: 960px) {
        flex-direction: column;
        padding: 80px 24px;

        .headline {
          font-size: 36px;
        }
      }
    }
    .swiper-section-heating-ventilation {
      background-color: #fff;
    }
    .hvac-solutions-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 60px;
      padding: 0px 95px 94px 113px;
      background-color: #ffffff;

      .hvac-text {
        flex: 1 1 50%;
        .hvac-title {
          font-family: Inter !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          line-height: 125%;
          letter-spacing: 0%;

          margin-bottom: 48px;
        }

        .hvac-description {
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 20px;
          // line-height: 100%;
          letter-spacing: 0%;
          max-width: 535px;
          margin-bottom: 20px;
        }
      }

      .hvac-image {
        flex: 1 1 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .responsive-image {
          width: 100%;
          height: auto;
          max-width: 634px;
          // min-height: 400;
        }
      }

      @media (max-width: 960px) {
        flex-direction: column;
        padding: 60px 24px;

        .hvac-text {
          text-align: center;
        }

        .hvac-title {
          font-size: 32px;
        }
      }
    }
    .empowering-all-conatiner {
      width: 100%;
      background-color: #ffffff;

      .full-width-image {
        width: 100% !important;
        height: auto !important;
        display: block;
      }
    }
  }
}
