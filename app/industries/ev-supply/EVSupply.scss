.ev-supply-page-container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;

  .ev-supply-page-content {
    width: 100vw;
    min-height: 100vh;
    position: relative;

    .ev-supply-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      width: 100vw;
      position: relative;

      .ellipse-container {
        width: 100vw;
        height: 100vh;
        position: relative;
        z-index: 1;

        .banner-gradient {
          position: absolute;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          pointer-events: none;
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0.5) 100%
          );
          z-index: 2;
        }

        img {
          width: 100vw;
          height: 100vh;
          object-fit: cover;
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }
      }

      .hero-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 999px;
        height: 124px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3;
        color: #fff;
        text-align: center;
        padding-bottom: 0;

        .title {
          font-family: Inter !important;
          font-weight: 800;
          font-style: Extra Bold;
          font-size: 64px;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
          width: 100%;
          height: 100%;
          margin: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #d4d8da;
        }
      }
    }
    .ev-supply-mobility-section {
      display: flex;
      justify-content: space-between;
      padding: 110px 126px 137px 92px;
      background-color: #fff;
      gap: 123px;

      @media (max-width: 1024px) {
        flex-direction: column;
        padding: 60px 24px;
      }

      .ev-supply-mobility-left {
        flex: 1;
        display: flex;
        align-items: flex-start;
        justify-content: center;

        .mobility-title {
          min-width: 517px;
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          color: #d77d46;
        }
      }

      .ev-supply-mobility-right {
        flex: 1;
        display: flex;
        align-items: center;

        .mobility-description {
          max-width: 582px;
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 20px;
          letter-spacing: 0%;
          color: #021f2e;
        }
      }
    }

    .evse-skills-section {
      display: flex;
      justify-content: space-between;
      padding: 101px 0 101px 94px;
      gap: 40px;
      background-color: #fff;

      @media (max-width: 1024px) {
        flex-direction: column;
        padding: 60px 24px;
      }

      .evse-skills-left {
        flex: 1;
        display: flex;
        align-items: flex-start;
        // justify-content: center;

        .evse-title {
          font-family: Inter !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          color: #d77d46;
          max-width: 375px;
        }
      }

      .evse-skills-right {
        flex: 1.5;

        .evse-skill-list {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            display: flex;
            align-items: center;
            gap: 12px;
            font-family: Montserrat !important;
            font-weight: 500;
            font-style: Medium;
            font-size: 16px;
            line-height: 20px;
            letter-spacing: 0%;
            color: #021f2e;
            margin-bottom: 16px;

            img {
              width: 20px;
              height: 20px;
            }
          }
        }
      }
    }
    .evse-iot-section {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 98px 92px 98px 94px;
      gap: 40px;
      background-color: #fff;

      @media (max-width: 1024px) {
        flex-direction: column;
        padding: 60px 24px;
      }

      .evse-iot-left {
        flex: 1;

        .evse-iot-title {
          font-family: Inter !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;

          margin-bottom: 27px;
        }

        .evse-iot-description {
          max-width: 503px;
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 16px;
          letter-spacing: 0%;
          color: #021f2e;
        }
      }

      .evse-iot-right {
        flex: 1;
        display: flex;
        // justify-content: center;
        align-items: center;

        .iot-diagram {
          max-width: 100%;
          height: 408px;
          object-fit: contain;
        }
      }
    }
    .evse-collab-section {
      padding: 165px 71px 70px 93px;
      text-align: center;
      position: relative;

      .evse-collab-text {
        font-family: Montserrat !important;
        font-weight: 700;
        font-style: Bold;
        font-size: 40px;
        letter-spacing: 0%;
        text-align: center;
        line-height: 120%;
        color: #ffffff;
      }

      .evse-divider {
        margin-top: 40px;
        width: 90%;
        height: 1px;
        background-color: rgba(65, 143, 186, 1);
        margin-left: auto;
        margin-right: auto;
      }
    }
    .ocpp-section {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 111px 94px 111px 94px;
      color: #ffffff;
      gap: 40px;
      background-image: url("/assets/images/png/ev-supply/maskgroup.svg");
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      min-height: 600px;

      @media (max-width: 1024px) {
        flex-direction: column;
        padding: 60px 24px;
      }

      .ocpp-left {
        flex: 1;

        .ocpp-title {
          font-family: Inter !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          line-height: 120%;
          margin-bottom: 46px;
        }

        .ocpp-description {
          max-width: 456px;
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 16px;
          letter-spacing: 0%;
        }
      }

      .ocpp-right {
        margin-top: 140px;
        flex: 1.2;

        .ocpp-list {
          list-style: none;
          padding: 0;

          li {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 20px;
            font-family: Montserrat !important;
            font-weight: 500;
            font-style: Medium;
            font-size: 16px;
            line-height: 20px;
            letter-spacing: 0%;

            img {
              width: 18px;
              height: 20px;
              flex-shrink: 0;
            }
          }
        }
      }
    }
  }
}
