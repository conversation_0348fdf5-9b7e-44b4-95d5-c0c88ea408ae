"use state";
import {
  Box,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
} from "@mui/material";
import Image from "next/image";
import "./SmartFarming.scss";
import {
  ExpertiseIcon,
  farmingdrone,
  SmartFarming,
  SupportImage,
  wedelieverline,
  WeEnsureicon,
  WhatWeSupportSmart,
} from "@/public";
import MechanicalTestingSwiper from "@/app/services/mechanical-design/MechanicalTestingSwiper";

const SmartFarmingPage = () => {
  const supportItems = [
    "Monitor farmland soil health, temperature, humidity and other parameters",
    "Optimizing irrigation based on climate data and plant needs",
    "Equipment Tracking through low-power, long-range communication",
    "Collecting and visualizing field data for better planning and analysis",
    "Wireless Connectivity: Wi-Fi, Bluetooth, Zigbee, Matter",
    "Use AI tools for weather forcasting and climate prediction",
    "Cloud-Based Applications with integrated Analytics",
  ];
  const data = [
    {
      number: "01",
      title: "Resource Optimization",
      description:
        "Get informed live data from  fields efficiently manage resources like water, soil moisture, weather, crop health, and more  to increase crop yields",
    },
    {
      number: "02",
      title: "Real-Time Monitoring",
      description:
        "Continuous track & analysis fields data using IoT sensors, gateways and software platforms for faster decision-making, reduces risks and boost productivity.",
    },
    {
      number: "03",
      title: "Wastage Reduction",
      description:
        "Helps reduction in Agri-wastage  by minimizing the  loss of resources such as water, fertilizers, seeds, labor, energy, and crop yield",
    },
    {
      number: "04",
      title: "Machinery Automation",
      description:
        "Add automation to your machinery by enabling sensor tech and connectivity solution to perform agricultural tasks with minimal human intervention, minimizes errors, and improves precision.",
    },
    {
      number: "05",
      title: "Precision Irrigation",
      description:
        "Deliver the right amount of water at right time and place by using IoT Technologies to minimize waste and maximize crop yield with resource-efficiency and sustainable agriculture.",
    },
  ];
  return (
    <Box className="smart-farming-page-container">
      <Box className="smart-farming-page-content">
        <Box className="smart-farming-page-header">
          <Box className="ellipse-container">
            <Image src={SmartFarming} alt="Ellipse" className="ellipse-image" />
          </Box>
        </Box>
        <Box className="smart-farming-info-section">
          <Box className="left-content">
            <Typography className="info-title">
              Data-Driven Decisions for <br />
              Sustainable and Profitable <br />
              Agriculture yield.
            </Typography>
          </Box>
          <Box className="right-content">
            <Typography variant="body1" className="info-description">
              We combine cutting-edge technology with traditional farming
              knowledge to help growers maximize efficiency, boost yields, and
              reduce environmental impact.
            </Typography>
            <Typography variant="body1" className="info-description-two">
              Aadvik TekLabs leverages digital technologies to monitor,
              automate, and analyze farm operations by integrating IoT, data
              analytics, and sensor technology, enabling precision insights to
              effectively manage fields, crops, and livestock.
            </Typography>
          </Box>
        </Box>
        <Box className="smart-farming-support-section">
          <Box className="support-left">
            <Typography className="support-title">
              Where we can Support !
            </Typography>
            <List className="support-list">
              {supportItems.map((item, index) => (
                <ListItem key={index} className="support-item">
                  <ListItemIcon>
                    <Image
                      src={ExpertiseIcon}
                      alt="icon"
                      width={20}
                      height={20}
                    />
                  </ListItemIcon>
                  <ListItemText primary={item} />
                </ListItem>
              ))}
            </List>
          </Box>

          <Box className="support-right">
            <Image
              src={WhatWeSupportSmart}
              alt="Support Illustration"
              className="support-image"
            />
          </Box>
        </Box>
        <Box className="smart-farming-feature-section">
          <Image
            src={wedelieverline}
            alt="Grid Background"
            className="grid-background"
            fill
          />

          {/* ───── left column ───── */}
          <Box className="feature-left">
            <Typography component="h2" className="feature-title">
              Empower Farmers and <br />
              Equipment Manufactures <br />
              for{" "}
              <span>
                Optimize Crop Yields <br />
                with Precision Farming.
              </span>
            </Typography>
          </Box>

          {/* ───── right column ───── */}
          <Box className="feature-right">
            <Image src={WeEnsureicon} alt="we-ensure-icon" />
            <Typography variant="h3" component="h3" className="feature-heading">
              We Deliver
            </Typography>
          </Box>
        </Box>

        {/* swiper section */}
        <Box className="swiper-section-smart-farming">
          <MechanicalTestingSwiper
            data={data}
            hoverTextColor="#021F2E"
            hoverTextColorDescription="#021F2E"
          />
        </Box>
        {/* Product Engineering Services */}
        <Box className="smart-farming-iot-section">
          {/* Top Heading */}
          <Box className="iot-top-heading">
            <Typography className="iot-main-heading">
              Product Engineering Services for Smart Farming, IoT Platforms,
              Gateways and SensorTech
            </Typography>
          </Box>

          <Box className="iot-content">
            {/* Left Text */}
            <Box className="iot-left-text">
              <Typography variant="body1" className="iot-paragraph">
                As agriculture embraces digital transformation, smart farming
                demands innovative solutions that integrate intelligent control
                systems with automated machinery and IoT-based devices.
                Forward-thinking farms are adopting energy-efficient
                technologies that align with sustainable practices and the
                evolving needs of modern agriculture.
              </Typography>
              <Typography variant="body1" className="iot-paragraph">
                Aadvik TekLabs partners with leading agritech companies to
                provide skilled engineering teams to add connectivity in their
                product to turns into fully functional, scalable solutions for
                Precision farming.
              </Typography>
            </Box>

            {/* Right Image */}
            <Box className="iot-image-container">
              <Image
                src={farmingdrone}
                alt="Smart Farming Drone"
                className="iot-image"
              />
            </Box>
          </Box>

          {/* Bottom Orange Highlighted Text */}
          <Box className="iot-bottom-highlight-container">
            <Typography variant="h5" className="iot-bottom-highlight">
              We embrace digital transformation in smart farming through IoT
              technologies to automated farming
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default SmartFarmingPage;
