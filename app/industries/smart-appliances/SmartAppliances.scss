.smart-appliances-page-container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;

  .smart-appliances-page-content {
    width: 100vw;
    min-height: 100vh;
    position: relative;

    .smart-appliances-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      width: 100vw;
      position: relative;

      .ellipse-container {
        width: 100vw;
        height: 100vh;
        position: relative;
        z-index: 1;

        .banner-gradient {
          position: absolute;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          pointer-events: none;
          background: linear-gradient(
            180deg,
            #35424a -9.09%,
            rgba(156, 158, 159) 100%
          );

          z-index: 2;
        }

        img {
          width: 100vw;
          height: 100vh;
          object-fit: cover;
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }
      }

      .hero-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 999px;
        height: 124px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3;
        color: #fff;
        text-align: center;
        padding-bottom: 0;
        gap: 90px;

        .title {
          min-width: 392px;
          font-family: Inter !important;
          font-weight: 800;
          font-style: Extra Bold;
          font-size: 64px;
          // line-height: 100%;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
          width: 100%;
          height: 100%;
          margin: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
    .smart-appliances-info-section {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 120px 143px 173px 92px;
      background-color: #fff;
      gap: 40px;

      .info-left {
        flex: 1;
        max-width: 45%;

        .info-title {
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          line-height: 120%;
          letter-spacing: 0%;
          color: #d77d46;
        }
      }

      .info-right {
        flex: 1;
        max-width: 40%;
        .info-description-container {
          max-width: 466px;
          .info-description {
            font-family: Montserrat !important;
            font-weight: 700;
            font-style: Bold;
            font-size: 16px;
            letter-spacing: 0%;

            strong {
              font-family: Montserrat !important;
              font-weight: 700;
              font-style: Bold;
              font-size: 16px;
              letter-spacing: 0%;
            }
          }
        }
      }
    }

    .smart-appliances-support-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 213px 75px 94px;
      gap: 40px;
      background-color: #fff;

      .support-left {
        flex: 1;
        max-width: 50%;

        .support-title {
          font-family: Inter !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          color: #021f2e;

          margin-bottom: 20px;
        }

        .support-list {
          padding: 0;

          .support-item {
            align-items: flex-start;

            .MuiListItemIcon-root {
              min-width: 30px;
            }

            .MuiListItemText-root {
              font-family: Montserrat !important;
              font-weight: 700 !important;
              font-style: Bold !important;
              font-size: 20px !important;
              color: #021f2e !important;
              line-height: 24px !important;
              letter-spacing: 0%;
              margin-top: auto;
              margin-bottom: auto;
            }
          }
        }
      }

      .support-right {
        flex: 1;
        max-width: 50%;
        display: flex;
        justify-content: center;

        .support-image {
          max-width: 100%;
          height: auto;
        }
      }

      @media (max-width: 768px) {
        flex-direction: column;
        padding: 40px 20px;

        .support-left,
        .support-right {
          max-width: 100%;
        }

        .support-title {
          font-size: 24px;
        }
      }
    }

    .devops-section {
      min-height: 538px;
      background-color: #021f2e;
      padding: 105px 145px 0px 94px;

      .devops-container {
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .devops-heading {
        font-family: Montserrat;
        font-weight: 600;
        font-size: 36px;
        line-height: 140%;
        color: #ffffff;

        .highlight {
          color: #d77d46;
          font-weight: 700;
        }
      }
      .devops-description-block {
        display: flex;
        justify-content: flex-end;
        min-width: 452px;
      }

      .devops-description-inner {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        max-width: 50%;
      }

      .devops-icon {
        width: 40px;
        height: 40px;
      }

      .devops-description {
        max-width: 452px;
        font-family: Montserrat !important;
        font-weight: 700;
        font-style: Bold;
        font-size: 16px;
        letter-spacing: 0%;
        color: #ffffff;
      }
    }

    .smart-appliances-deliver-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 72px 127px 145px 0px;
      gap: 110px;
      background-color: #fff;

      .deliver-left {
        flex: 1;
        max-width: 50%;
        display: flex;
        justify-content: center;

        .deliver-image {
          max-width: 100%;
          height: auto;
        }
      }

      .deliver-right {
        flex: 1;
        max-width: 50%;

        .deliver-title {
          font-family: Inter !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          margin-bottom: 46px;
        }

        .deliver-list {
          padding: 0;

          .deliver-item {
            align-items: flex-start;
            margin-bottom: 12px;

            .MuiListItemIcon-root {
              min-width: 30px;
            }

            .MuiListItemText-root {
              font-family: Montserrat !important;
              font-weight: 500;
              font-style: Medium;
              font-size: 16px;
              color: #021f2e;
              margin-top: auto;
              margin-bottom: auto;
            }
          }
        }
      }
    }
    .product-category-section {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      .product-category-image-wrapper {
        width: 100%;
        position: relative;
      }

      .product-category-image {
        width: 100%;
        height: auto;
        object-fit: contain;
        display: block;
      }
    }

    .smart-appliances-page-footer {
      background-color: #03202F;
      padding: 73px 0px;
      .smart-appliances-page-footer-content {
        display: flex;
        justify-content: center;
        .smart-appliances-page-footer-content-text {
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          text-align: center;
          color: #d77d46;
          max-width: 898px;
        }
      }
    }

    .icon-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr); // 7 icons per row
      gap: 20px;
      padding: 75px 234px;
      background-color: #03202F;
      justify-items: center;
      img {
        width: 76px;
        height: 76px;
      }
    }

    .icon-wrapper {
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
