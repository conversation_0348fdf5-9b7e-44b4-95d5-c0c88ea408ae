"use state";
import {
  Box,
  List,
  <PERSON>Item,
  <PERSON><PERSON><PERSON>Icon,
  <PERSON>ItemText,
  Typography,
} from "@mui/material";
import Image from "next/image";
import "./SmartAppliances.scss";
import {
  ExpertiseIcon,
  KeyproductImage,
  smartAppliancesbanner,
  SmartAppliancesBanner,
  SmartAppliancesImage1,
  SmartAppliancesImage10,
  SmartAppliancesImage11,
  SmartAppliancesImage12,
  SmartAppliancesImage13,
  SmartAppliancesImage14,
  SmartAppliancesImage2,
  SmartAppliancesImage3,
  SmartAppliancesImage4,
  SmartAppliancesImage5,
  SmartAppliancesImage6,
  SmartAppliancesImage7,
  SmartAppliancesImage8,
  SmartAppliancesImage9,
  SupportImage,
  WeEnsureicon,
  whatwedeliever,
} from "@/public";

const SmartAppliancesPage = () => {
  const supportItems = [
    "Energy Monitoring & Smart Scheduling",
    "Touch & Voice Interface Integration",
    "Mobile App Integration for Android & iOS",
    "Custom Embedded Firmware & HW Development",
    "Wireless Connectivity: Wi-Fi, Bluetooth, Zigbee, Matter",
    "Edge AI for Usage Optimization & Behavior Learning",
    "Cloud-Based Applications with Integrated Analytics",
  ];
  const deliverItems = [
    "Rapid Prototyping & Testing",
    "End-to-End Product Engineering Support",
    "Embedded Designing & Firmware Development",
    "Connectivity Solutions (Wi-Fi, BLE, Zigbee, LoRa, Matter)",
    "Mechanical Enclosure Design and Simulation Support",
    "Certification Readiness & Compliance Support",
    "Energy Efficiency & Sustainability Enhancements",
  ];

  const icons = [
    SmartAppliancesImage1,
    SmartAppliancesImage2,
    SmartAppliancesImage3,
    SmartAppliancesImage4,
    SmartAppliancesImage5,
    SmartAppliancesImage6,
    SmartAppliancesImage7,
    SmartAppliancesImage8,
    SmartAppliancesImage9,
    SmartAppliancesImage10,
    SmartAppliancesImage11,
    SmartAppliancesImage12,
    SmartAppliancesImage13,
    SmartAppliancesImage14,
  ];
  return (
    <Box className="smart-appliances-page-container">
      <Box className="smart-appliances-page-content">
        <Box className="smart-appliances-page-header">
          <Box className="ellipse-container">
            {/* <Image
              src={smartAppliancesbanner}
              alt="Ellipse"
              className="ellipse-image"
            /> */}
            {/* Gradient overlay goes here */}
            <Box className="banner-gradient" />
          </Box>

          <Box className="hero-content">
            <Image
              src={smartAppliancesbanner}
              alt="Ellipse"
              className="ellipse-image"
            />
            <Typography className="title" variant="h1">
              Smart Appliances for Smarter Living.
            </Typography>
          </Box>
        </Box>
        <Box className="smart-appliances-info-section">
          <Box className="info-left">
            <Typography variant="h2" className="info-title">
              Engineering Smart <br />
              Appliances with <br />
              Embedded Intelligence.
            </Typography>
          </Box>

          <Box className="info-right">
            <Box className="info-description-container">
              <Typography variant="body1" className="info-description">
                Aadvik TekLabs specializes in embedded systems, industrial
                software, communication design, and backend integration to drive
                innovation in home and utility appliances. We partner with
                manufacturers and engineering teams to build intelligent,
                feature-rich solutions that meet the demands of connected
                living, delivering seamless user experiences and advanced
                functionality.
              </Typography>
            </Box>
          </Box>
        </Box>
        {/* where we can support */}
        <Box className="smart-appliances-support-section">
          <Box className="support-left">
            <Typography variant="h3" className="support-title">
              Where we can Support !
            </Typography>
            <List className="support-list">
              {supportItems.map((item, index) => (
                <ListItem key={index} className="support-item">
                  <ListItemIcon>
                    <Image
                      src={ExpertiseIcon}
                      alt="icon"
                      width={20}
                      height={20}
                    />
                  </ListItemIcon>
                  <ListItemText primary={item} />
                </ListItem>
              ))}
            </List>
          </Box>

          <Box className="support-right">
            <Image
              src={SupportImage}
              alt="Support Illustration"
              className="support-image"
            />
          </Box>
        </Box>

        <Box className="devops-section">
          <Box className="devops-container">
            <Typography className="devops-heading">
              We ensures that every <br />
              appliance is not only <br />
              connected, but intelligent, <br />
              <span className="highlight">intuitive and future ready.</span>
            </Typography>
            <Box className="devops-description-block">
              <Box className="devops-description-inner">
                <Image src={WeEnsureicon} alt="icon" className="devops-icon" />
                <Typography className="devops-description">
                  With Aadvik TekLabs, boost your product engineering to enhance
                  performance, reliability, and quality. We accelerate
                  development, ensure global compliance, and integrate smart,
                  energy-efficient technologies to keep you ahead in a connected
                  world.
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>

        <Box className="smart-appliances-deliver-section">
          {/* LEFT: Image */}
          <Box className="deliver-left">
            <Image
              src={whatwedeliever}
              alt="What We Deliver"
              className="deliver-image"
            />
          </Box>

          {/* RIGHT: Text Content */}
          <Box className="deliver-right">
            <Typography variant="h3" className="deliver-title">
              What We Deliver
            </Typography>
            <List className="deliver-list">
              {deliverItems.map((item, index) => (
                <ListItem key={index} className="deliver-item">
                  <ListItemIcon>
                    <Image
                      src={ExpertiseIcon}
                      alt="icon"
                      width={22}
                      height={22}
                    />
                  </ListItemIcon>
                  <ListItemText primary={item} />
                </ListItem>
              ))}
            </List>
          </Box>
        </Box>

        {/* Product Category */}
        <Box className="product-category-section">
          <Box className="product-category-image-wrapper">
            <Image
              src={KeyproductImage}
              alt="Product Category"
              className="product-category-image"
              layout="responsive"
            />
          </Box>
        </Box>

       
        {/* Lastsection */}
        <Box className="icon-grid">
          {icons.map((Icon, index) => (
            <Box className="icon-wrapper" key={index}>
              <Image
                src={Icon}
                alt={`Icon ${index + 1}`}
                className="icon-svg"
              />
            </Box>
          ))}
        </Box>
         <Box className="smart-appliances-page-footer">
          <Box className="smart-appliances-page-footer-content">
            <Typography className="smart-appliances-page-footer-content-text">
              We build the next gen intelligent appliances - smarter, faster,
              and cleaner{" "}
            </Typography>
          </Box>
        </Box>

      </Box>
    </Box>
  );
};

export default SmartAppliancesPage;
