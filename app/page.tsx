import { Box, Divider } from "@mui/material";
import AboutUS from "@/components/AboutUS/About";
import WhatWeDo from "@/components/WhatWeDo/WhatWeDo";
import WhyUs from "@/components/WhyUS/WhyUs";
import IndustriesSlider from "@/components/IndustriesSlider/IndustriesSlider";
import HowWeWork from "@/components/HowWeWork/HowWeWork";
import ConnectForm from "@/components/ConnectForm/ConnectForm";
import OldHeroSection from "@/components/OldHeroSection/OldHeroSection";
import OurValues from "@/components/OurValues/OurValues";
// import "@/app/page.scss";

export default function Home() {
  return (
    <Box className="home-page-container">
      <Box className="home-page-content">
        <Box>
          <OldHeroSection />
        </Box>
        <Box>
          <IndustriesSlider />
        </Box>
        <Box>
          <AboutUS />
        </Box>
        <Box>
          <WhatWeDo />
        </Box>
        <Box>
          <HowWeWork />
        </Box>
        <Box>
          <OurValues />
        </Box>
        <Box>
          <WhyUs />
        </Box>
        <Box>
          <ConnectForm
            heading={
              <>
                Let us know how we can{" "}
                <span className="highlight">collaborate</span> to create
                something <span className="highlight">valuable</span> for your{" "}
                <span className="highlight">business</span>.
              </>
            }
          />
        </Box>
      </Box>
    </Box>
  );
}
