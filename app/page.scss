.home-page-container {
  .home-page-content {
    height: 100%;

    .about-section {
      .about-us-section {
        background: #00121c;

        .about-label {
          color: #d9d9d9;
        }

        .about-heading {
          color: #d9d9d9;

          span {
            background: linear-gradient(90deg, #6bd0b9 10.58%, #404a78 62.02%);
            background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }

        .info-text {
          color: #d8d5d5;
          margin-bottom: 0px;
        }
      }
    }

    .what-section {
      .what-we-do-section {
        background: #00121c;

        .title {
          color: #d9d9d9;
        }

        .description {
          color: #d8d5d5;
        }

        .grid-item {
          border: 0.5px solid;
          border-image-source: linear-gradient(
            270deg,
            #030303 -50.98%,
            #1e4154 4.08%,
            #418fba 48.49%,
            #030303 129.31%
          );
          border-image-slice: 1;

          &:hover {
            background-color: #418f7d0f;
          }
        }

        .index {
          background: radial-gradient(
            50% 50% at 50% 50%,
            #8cffe4 0%,
            #404a78 100%
          ) !important;
          background-clip: text !important;
          -webkit-text-fill-color: transparent !important;
          color: transparent !important;
        }

        .service {
          background: radial-gradient(
            50% 50% at 50% 50%,
            #8cffe4 0%,
            #404a78 100%
          ) !important;
          background-clip: text !important;
          -webkit-text-fill-color: transparent !important;
        }
      }
    }

    .why-section {
      .why-us-section {
        background: #00121c;

        .section-title {
          color: #d9d9d9;
        }

        .section-description {
          color: #d8d5d5;
        }

        .feature-item {
          &:hover {
            background: #418f7d0f;
          }
        }

        .label {
          background: radial-gradient(
            50% 50% at 50% 50%,
            #8cffe4 0%,
            #404a78 100%
          );
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }

    .how-section {
      border-top: 1px solid #1e4154;

      .How-we-work-container {
        background: #00121c;

        .leftSection {
          .heading {
            color: #d9d9d9;
          }

          .description {
            color: #d8d5d5;
          }
        }

        .rightSection {
          .stepNumber {
            background: linear-gradient(90deg, #6bd0b9 10.58%, #404a78 62.02%);
            background-clip: text;
            -webkit-text-fill-color: transparent;

            &:hover {
              background: linear-gradient(
                90deg,
                #6bd0b9 10.58%,
                #404a78 62.02%
              );
            }
          }

          .stepCard {
            border: Mixed solid;
            border-image-source: linear-gradient(
              270deg,
              #030303 -50.98%,
              #1e4154 4.08%,
              #418fba 48.49%,
              #030303 129.31%
            );
            border-image-slice: 1;
            background: #030303;
          }
        }
      }
    }

    .our-section {
      .our-values-section {
        background: #00121c;
      }
    }

    .connect-section {
      .connect-container {
        .form-input fieldset {
          border: 1px solid;

          border-image-source: linear-gradient(
            270deg,
            #1e4154 4.08%,
            #418fba 48.49%
          );
          border-image-slice: 1;
          border-radius: 8px;
        }

        .highlight {
          background: linear-gradient(90deg, #6bd0b9 10.58%, #404a78 62.02%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}
