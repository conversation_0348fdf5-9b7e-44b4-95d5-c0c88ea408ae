@use '../styles/variables' as *;
@use '../styles/aos-mixins' as *;
@use '../styles/animations';
@import "tailwindcss";

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-family: $font-poppins !important;
  overflow-x: hidden;
  width: 100%;
}

body {
  background: $body-bg;
  color: var(--foreground);
  font-family: $font-montserrat;
  margin: 0;
  padding: 0;
}

/* Font declarations */
.font-prata {
  font-family: $font-prata !important;
}

/* Override Material-UI's default font */
.MuiTypography-root,
.MuiButton-root,
.MuiAppBar-root,
button,
div {
  font-family: $font-poppins !important;
}

:root {
  --font-prata: var(--font-prata), serif;
  --font-montserrat: var(--font-montserrat), sans-serif;
}