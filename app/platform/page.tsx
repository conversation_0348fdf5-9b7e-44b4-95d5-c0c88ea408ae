import { Box, Typography } from "@mui/material";
import "./PlatformPage.scss";
import {
  hero_bg_ellipse,
  platform_hero_img,
  feature_icon1,
  feature_icon2,
  feature_icon3,
  feature_icon4,
  feature_icon5,
  feature_icon6,
} from "@/public/index";
import Image from "next/image";
import ConnectForm from "@/components/ConnectForm/ConnectForm";
import BottomTextBanner from "@/components/BottomTextBanner/BottomTextBanner";
import PlatformTabs from "@/components/platforms-tabs/platform-tabs";
import { Sync } from "@mui/icons-material";
import SyncMasterForm from "@/components/SyncMasterForm/SyncMasterForm";

const PlatformPage = () => {
  return (
    <Box className="platform-page-container">
      <Box className="platform-page-content">
        <Box className="platform-page-header">
          <Box className="ellipse-container">
            <Image
              src={hero_bg_ellipse}
              alt="Ellipse"
              className="ellipse-image"
            />
          </Box>

          <Box
            className="hero-content"
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              zIndex: 2,
              color: "#fff",
              textAlign: "center",
            }}
          >
            <Typography className="title" variant="h1">
              SyncMaster
            </Typography>

            <Box
              sx={{
                width: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                my: 2,
              }}
            >
              <Box
                sx={{
                  flex: 1,
                  height: "1px",
                  // background:
                  //   "linear-gadient(90deg, #030303 -50.98%, #1E4154 4.08%, #418FBA 48.49%, #030303 129.31%)",
                  background: "#ffffff",
                  borderRadius: "2px",
                  mr: 2,
                  marginLeft: "150px",
                }}
              />
              <Typography
                sx={{
                  fontWeight: 800,
                  fontSize: "24px",
                  color: "#FFFFFF",
                  whiteSpace: "nowrap",
                  textAlign: "center",
                  px: 2,
                  // background: "rgba(30,65,84,0.7)",
                  borderRadius: "8px",
                }}
                className="text-divider"
              >
                Your Seamless Growth Engine
              </Typography>
              <Box
                sx={{
                  flex: 1,
                  height: "1px",
                  // background:
                  //   "linear-gradient(90deg, #030303 -50.98%, #1E4154 4.08%, #418FBA 48.49%, #030303 129.31%)",
                  background: "#ffffff",
                  borderRadius: "2px",
                  ml: 2,
                  marginRight: "150px",
                }}
              />
            </Box>
            <Box className="platform-image-container">
              <Image
                src={platform_hero_img}
                alt="Platform Hero"
                className="hero-image"
              />
            </Box>
          </Box>
        </Box>

        <Box className="about-section">
          <Typography className="title">About SyncMaster</Typography>

          <Box className="description-section">
            <Typography className="description">
              SyncMaster is a <span>unified platform</span> that streamlines
              core business operations, covering HRM, Project Management,
              Finance, Payroll, and CRM within a single user-friendly interface.
            </Typography>
            <Box className="description-box">
              <Typography className="description-2">
                The platform empowers business leaders to automate routine
                tasks, monitor key performance metrics, and support strategic
                decision making. driving efficiency, productivity, and growth
                across the organization.
              </Typography>
              <Typography className="description-2">
                Platform enables real-time task assignment, resource allocation,
                project monitoring giving management instant insights into
                <span> organizational performance</span> and client project
                progress.
              </Typography>
              <Typography className="description-2">
                Integrated with built-in CRM tools for mass email campaigns and
                client feedback, SyncMaster enhances collaboration, boosts
                client satisfaction, and supports smarter, faster
                decision-making, all from one centralized system
              </Typography>
            </Box>
          </Box>
        </Box>

        <Box className="feature-section">
          <Typography className="title">Features</Typography>

          <Box className="feature-grid">
            {[
              { title: "Integrated HRMS", icon: feature_icon1 },
              { title: "Integrated CRM", icon: feature_icon2 },
              { title: "Finance & Payroll", icon: feature_icon3 },
              { title: "Asset Management", icon: feature_icon4 },
              { title: "Operation Management", icon: feature_icon5 },
              { title: "Client Engagement", icon: feature_icon6 },
            ].map((feature, idx) => (
              <Box className="feature-item" key={idx}>
                <Typography className="item-title">{feature.title}</Typography>
                <Image
                  src={feature.icon}
                  alt={feature.title + " Icon"}
                  className="feature-icon"
                />
              </Box>
            ))}
          </Box>
        </Box>

        <Box className="platform-components-section">
          <PlatformTabs />
        </Box>

        <Box>
          <BottomTextBanner
            text="Aadvik TekLabs partners with clients by merging domain expertise and tech leadership to create transformative, forward-thinking solutions."
            highlight="partners"
          />
        </Box>

        {/* Connect with us sectyion */}
        <Box className="connect-section">
          <Typography className="title">
            Better Business judgment needs meaningful data , Switch to
            SyncMaster now !
          </Typography>
          <SyncMasterForm
            heading={
              <>
                Connect with our sales team today{" "}
                <span className="highlight">for demo and user guide !</span>
              </>
            }
          />
        </Box>
      </Box>
    </Box>
  );
};

export default PlatformPage;
