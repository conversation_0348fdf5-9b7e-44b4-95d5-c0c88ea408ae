import React from "react";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import {
  what_we_do_icon1,
  what_we_do_icon2,
  what_we_do_icon3,
  what_we_do_icon4,
  what_we_do_icon5,
  what_we_do_icon6,
  what_we_do_icon7,
  what_we_do_icon8,
  what_we_do_icon9,
  what_we_do_icon11,
  what_we_do_icon12,
  what_we_do_icon10,
} from "@/public/index";
import "./WhatWeDo.scss";

const services = [
  {
    title: "Embedded Design & \n Development Services",
    icon: what_we_do_icon1,
  },
  { title: "Cloud And Mobility \n Solutions", icon: what_we_do_icon2 },
  { title: "AI & Machine \n Learning", icon: what_we_do_icon3 },
  { title: "Wireless Connectivity \n Enablement", icon: what_we_do_icon4 },
  { title: "Web Application \n Development", icon: what_we_do_icon5 },
  { title: "Mobile App \n Solutions", icon: what_we_do_icon6 },
  { title: "UI-UX Designing", icon: what_we_do_icon7 },
  { title: "AR/VR Based Solutions", icon: what_we_do_icon8 },
  { title: "3D Model Creation", icon: what_we_do_icon9 },
  { title: "Technology \n Consulting", icon: what_we_do_icon10 },
  { title: "Regulatory & \n Certification Support", icon: what_we_do_icon11 },
  { title: "3D Modeling & \n Mechanical Designs", icon: what_we_do_icon12 },
];

const WhatWeDo = () => {
  return (
    <>
      <section className="what-we-do-section">
        <Box className="what-we-do-container">
          {/* Left Title Block */}
          <Box className="left-block">
            <Typography variant="h5" className="title">
              What We Do ?
            </Typography>
            <Typography className="description">
              We build innovative and intelligent business platforms across Web,
              Mobile, and Digital Platforms, leveraging the latest technology
              stacks—<b>including AI, ML, IoT, Cloud, Blockchain, and more.</b>
              Our solutions are designed to innovate and excel through
              state-of-the-art, impactful, and future-ready solutions.
            </Typography>
          </Box>

          {/* Right Grid Block */}
          <Box className="grid-block">
            {services.map((service, index) => (
              <Box key={index} className="grid-item">
                <Typography className="index">
                  {" "}
                  {index + 1 < 10 ? `0${index + 1}` : index + 1}
                </Typography>
                <Box className="icon-wrapper">
                  <Image
                    src={service.icon}
                    alt={`Service Icon ${index + 1}`}
                    width={100}
                    height={100}
                    className="icon"
                    style={{ objectFit: "contain" }}
                  />
                </Box>
                <Typography className="service">
                  {service.title.split("\n").map((line, i) => (
                    <React.Fragment key={i}>
                      {line}
                      {i !== service.title.split("\n").length - 1 && <br />}
                    </React.Fragment>
                  ))}
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>
      </section>
      <Box className="bottom-text-container">
        <Typography className="bottom-text">
          At <span className="highlight">Aadvik TekLabs</span>, we combine deep
          domain expertise with technology leadership to develop impactful,
          future-ready solutions.
        </Typography>
      </Box>
    </>
  );
};

export default WhatWeDo;
