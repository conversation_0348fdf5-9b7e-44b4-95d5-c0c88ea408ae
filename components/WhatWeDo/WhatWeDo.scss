.what-we-do-section {
  background-color: #f6f6f6;
  padding: 0px 5% 100px;
  font-family: "Inter", sans-serif;

  .what-we-do-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .left-block {
      flex: 1;
      min-width: 300px;

      .title {
        font-family: Inter !important;
        font-weight: 700;
        font-size: 40px;

        letter-spacing: 0%;
        padding-bottom: 25px;
        border-bottom: 1px solid #021f2e;
      }

      .description {
        margin-top: 69px;
        font-family: Montserrat !important;
        font-weight: 500;
        font-size: 16px;

        letter-spacing: 0%;
        max-width: 400px;
        margin-bottom: 70px;
        color: #021f2e;

        b {
          font-family: Montserrat !important;
          font-weight: 700;
          font-size: 16px;

          letter-spacing: 0%;
        }
      }
    }

    .grid-block {
      flex: 2;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      // gap: 1px;
      // background-image: url('/assets/images/png/grid_bg.png');
      // background-size: 100% 100%; // Stretch to fit width and height
      // background-position: center;
      // background-repeat: no-repeat;
      // min-height: 600px;

      .grid-item {
        padding: 100px 0px 36px 40px;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        border: 0.5px solid;
        border-image-source: linear-gradient(
          270deg,
          #030303 -50.98%,
          #1e4154 4.08%,
          #418fba 48.49%,
          #030303 129.31%
        );
        border-image-slice: 1;
        border: 0.5px solid #d77d46;

        // hover
        &:hover {
          background: #d77d4629;

          .index {
            font-family: Montserrat !important;
            font-weight: 700;
            font-style: italic;
            font-size: 20px;
            line-height: 100%;
            letter-spacing: 0%;
            vertical-align: middle;
          }

          .service {
            font-family: Montserrat !important;
            font-weight: 600;
            font-size: 24px;
            line-height: 100%;
            letter-spacing: 0%;
            vertical-align: middle;
            color: #021f2e;
          }

          .icon {
            filter: brightness(0) saturate(100%) invert(56%) sepia(89%)
              saturate(684%) hue-rotate(341deg) brightness(93%) contrast(95%);
          }
        }

        .index {
          font-family: Montserrat;
          font-weight: 400;
          font-size: 20px;
          line-height: 100%;
          letter-spacing: 0%;
          vertical-align: middle;
        }

        .icon-wrapper {
          width: 100px;
          height: 100px;
          display: flex;
          align-items: center;
          // justify-content: center;
          // margin: 52px 0 33px -10px;
          margin-top: 57px;

          .icon {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
          }
        }

        .service {
          font-family: Montserrat;
          font-weight: 400;
          font-size: 24px;

          letter-spacing: 0%;
          vertical-align: middle;

          background: #021f2e;
          background-clip: text;
          -webkit-text-fill-color: transparent;

          &:first-child {
            font-style: italic;
            font-weight: 600;
          }
        }
      }

      // ⬇️ Add this below `.grid-item`
      .grid-item:nth-child(3n + 1) {
        border-left: none;
      }

      .grid-item:nth-child(3n) {
        border-right: none;
      }
    }
  }
}

.bottom-text-container {
  background-color: #021f2e;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background-image: url("/assets/images/png/banner_bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  // min-height: 300px; // Adjust as needed

  .bottom-text {
    font-family: Inter !important;
    font-weight: 800;
    font-size: 48px;

    letter-spacing: 0%;
    text-align: center;
    max-width: 900px;
    padding: 93px 0px 68px;
    color: #d8d5d5;

    .highlight {
      // background: linear-gradient(90deg, #6BD0B9 10.58%, #404A78 62.02%);
      background: #d77d46;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
