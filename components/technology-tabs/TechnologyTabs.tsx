"use client";
import React, { useState } from "react";
import { Tabs, Tab, Box } from "@mui/material";
import "./TechnologyTabs.scss";

const tabData = [
  {
    label: "Front End",
    img: "/assets/images/png/frontend.png",
    alt: "Front End",
  },
  {
    label: "Back End",
    img: "/assets/images/png/backend.png",
    alt: "Back End",
  },
  {
    label: "Database",
    img: "/assets/images/png/database.png",
    alt: "Database",
  },
  {
    label: "Deployement",
    img: "/assets/images/png/deployement.png",
    alt: "Deployement",
  },
];

const TechnologyTabs = () => {
  const [value, setValue] = useState(0);
  const [fade, setFade] = useState(true);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setFade(false);
    setTimeout(() => {
      setValue(newValue);
      setFade(true);
    }, 200); // duration matches CSS
  };

  return (
    <Box className="technology-tabs-container" sx={{ width: "100%" }}>
      <Tabs value={value} onChange={handleChange} centered>
        {tabData.map((tab, idx) => (
          <Tab key={tab.label} label={tab.label} />
        ))}
      </Tabs>
      <Box sx={{ mt: 3, display: "flex", justifyContent: "center" }}>
        <img
          src={tabData[value].img}
          alt={tabData[value].alt}
          className={`tech-tab-img${fade ? " fade-in" : " fade-out"}`}
          style={{ maxWidth: "100%", height: "auto" }}
        />
      </Box>
    </Box>
  );
};

export default TechnologyTabs;
