import { Box, Typography } from "@mui/material";
import "./ConnectivityCard.scss";

interface ConnectivityCardProps {
  title: string;
  technologies: string;
  backgroundColor?: string;
}

const ConnectivityCard: React.FC<ConnectivityCardProps> = ({
  title,
  technologies,
  backgroundColor,
}) => {
  return (
    <Box
      className="connectivity-card"
      style={{ backgroundColor }}
    >
      <Typography
        variant="h6"
        className="connectivity-card-title"
      >
        {title}
      </Typography>
      <Typography
        variant="body1"
        className="connectivity-card-technologies"
      >
        {technologies}
      </Typography>
    </Box>
  );
};

export default ConnectivityCard;