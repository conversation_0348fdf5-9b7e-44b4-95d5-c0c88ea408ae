.what-we-offer {
  &__title {
    font-family: Inter !important;
    font-weight: 700 !important;
    font-style: Bold;
    font-size: 40px !important;
    line-height: 100%;
    letter-spacing: 0%;
    color: #021F2E !important;
    margin-bottom: 49px !important;
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
    }
  }

  &__card {
    padding: 32px 24px;
    border-radius: 12px;
    background-color: #ffffff;
    transition: all 0.3s ease;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    &:hover {
      transform: translateY(-4px);

      background-color: #d77d462b;

      .what-we-offer__service-title {
        font-family: Montserrat !important;
        font-weight: 700 !important;
        font-style: Bold;
        font-size: 16px;
        line-height: 100%;
        letter-spacing: 0%;
        vertical-align: middle;
      }
    }

    &--highlighted {
      background-color: #f5f1eb;
      border: 1px solid #e8ddd4;
    }
  }

  &__icon {
    margin-bottom: 20px;
  }
  &__service {
    max-width: 315px;
  }

  &__service-title {
    font-family: Montserrat !important;
    font-weight: 500;
    font-style: Medium;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #d77d46;
  }
}
