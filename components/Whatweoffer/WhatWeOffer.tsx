"use client"; // Required for client-side interactivity like onClick

import type React from "react";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import "./Whatweoffer.scss";
import { ServiceIcon } from "@/public";

interface Service {
  title: string;
}

interface WhatWeOfferProps {
  services: Service[];
  onServiceClick?: (service: Service) => void;
}

const WhatWeOffer: React.FC<WhatWeOfferProps> = ({ services, onServiceClick }) => {
  return (
    <Box className="what-we-offer">
      <Box className="what-we-offer__grid">
        {services.map((service, index) => (
          <Box
            key={index}
            className="what-we-offer__card"
            onClick={() => onServiceClick?.(service)} // Trigger onServiceClick with the service
            sx={{ cursor: "pointer" }} // Add cursor pointer for better UX
          >
            <Box className="what-we-offer__icon">
              <Image
                src={ServiceIcon}
                alt="Service Icon"
                width={39}
                height={45}
              />
            </Box>
            <Box className="what-we-offer__service">
              <Typography className="what-we-offer__service-title">
                {service.title}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default WhatWeOffer;