@import '../../styles/variables.scss';

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }

    to {
        transform: translateY(0);
    }
}

.cookie-consent-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: #fff;
    border-top: 1px solid rgba(0, 0, 0, 0.12);
    padding: 1rem;
    animation: slideUp 0.3s ease-out;

    .cookie-consent-container {
        // max-width: 1600px;
        width: 100%;
        margin: 0;

        .cookie-consent-content {
            width: 100%;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 15px;

            @media (min-width: 768px) {
                padding: 25px 40px;
                gap: 16px;
            }

            @media (min-width: 1200px) {
                padding: 30px 60px;
                gap: 18px;
            }

            @media (min-width: 1400px) {
                padding: 35px 80px;
                gap: 20px;
            }

            @media (min-width: 1600px) {
                padding: 13px 68px 68px 122px;
                gap: 20px;
            }

            .cookie-consent-title {
                font-family: Poppins;
                font-weight: 600;
                font-size: 1.5rem;
                line-height: 1.2;
                letter-spacing: 0%;
                vertical-align: middle;
                background: #000000;
                background-clip: text;
                text-align: center;
                margin-bottom: 1rem;

                @media (min-width: 768px) {
                    font-size: 1.75rem;
                    margin-bottom: 18px;
                }

                @media (min-width: 1200px) {
                    font-size: 32px;
                    margin-bottom: 20px;
                }

                @media (min-width: 1400px) {
                    font-size: 36px;
                    margin-bottom: 22px;
                }

                @media (min-width: 1600px) {
                    font-size: 40px;
                    line-height: 30px;
                    margin-bottom: 24px;
                }
            }

            .cookie-consent-text {
                font-family: Poppins;
                font-weight: 400;
                font-size: 0.875rem;
                margin-bottom: 0.75rem;
                line-height: 1.5;
                letter-spacing: 0%;
                text-align: justify;
                vertical-align: middle;

                @media (min-width: 768px) {
                    font-size: 1rem;
                }

                @media (min-width: 1200px) {
                    font-size: 18px;
                    line-height: 1.6;
                }

                @media (min-width: 1400px) {
                    font-size: 19px;
                    line-height: 1.6;
                }

                @media (min-width: 1600px) {
                    font-size: 20px;
                    line-height: 30px;
                }

                a,
                button {
                    font-family: Poppins;
                    font-weight: 700;
                    font-size: inherit;
                    line-height: inherit;
                    letter-spacing: 0%;
                    text-align: justify;
                    vertical-align: middle;
                    text-decoration: underline;
                    text-decoration-style: solid;
                    text-transform: none;
                    background: transparent;
                    padding: 0;
                    color: inherit;

                    &:hover {
                        opacity: 0.8;
                    }
                }
            }

            .cookie-consent-buttons {
                display: flex;
                flex-direction: column;
                gap: 10px;
                // width: 100%;
                margin-top: 1rem;
                align-items: flex-end; // Align buttons to the end

                @media (min-width: 768px) {
                    flex-direction: row;
                    gap: 16px;
                    justify-content: flex-end;
                }

                @media (max-width: 767px) {
                    flex-direction: column;
                    gap: 0.5rem;
                    align-items: stretch; // Stretch buttons on mobile

                    button {
                        width: 100%;
                    }
                }

                button {
                    font-family: Poppins;
                    font-weight: 500;
                    font-size: 16px;
                    line-height: 1.4;
                    letter-spacing: 0%;
                    text-align: center;
                    vertical-align: middle;
                    background: #4988C8;
                    color: $text-white;
                    text-transform: none;
                    border-radius: 30px;
                    transition: background-color 0.2s ease;
                    min-width: 120px;
                    width: fit-content; // Use fit-content instead of 100%
                    height: fit-content; // Ensure consistent height
                    white-space: nowrap; // Prevent text wrapping
                    padding: 12px 24px; // Consistent padding

                    @media (min-width: 768px) {
                        font-size: 18px;
                        line-height: 1.5;
                        min-width: 150px;
                        padding: 14px 32px;
                    }

                    @media (min-width: 1200px) {
                        font-size: 19px;
                        padding: 15px 40px;
                    }

                    @media (min-width: 1400px) {
                        font-size: 20px;
                        padding: 16px 48px;
                    }

                    @media (min-width: 1600px) {
                        line-height: 30px;
                        padding: 16px 52px;
                    }

                    &:first-of-type {
                        background-color: #1976d2;
                        color: white;

                        &:hover {
                            background-color: #1565c0;
                        }
                    }

                    &:last-of-type {
                        background-color: #1976d2;
                        color: white;

                        &:hover {
                            background-color: #1565c0;
                        }
                    }
                }
            }

        }
    }

    @media (min-width: 768px) {
        padding: 1.5rem;
    }

    @media (min-width: 1200px) {
        padding: 2rem;

        .cookie-consent-container {
            padding: 0 2rem;
        }
    }
}