"use client";
import "./CookieConsent.scss";
import { useState, useEffect } from "react";
import { Box, Container, Typography, Button, Paper } from "@mui/material";
import { COOKIE_CONSENT } from "@/constant";
import CookieDialog from "../CookieDialog/CookieDialog";
import CookiePolicyDialog from "../CookiePolicyDialog/CookiePolicyDialog";

const CookieConsent = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isPolicyDialogOpen, setIsPolicyDialogOpen] = useState(false);

  useEffect(() => {
    // Check if user has already made a choice
    const cookieChoice = localStorage.getItem("cookieConsent");
    if (!cookieChoice) {
      setIsVisible(true);
    }
  }, []);

  const handleAcceptAll = () => {
    localStorage.setItem("cookieConsent", "all");
    setIsVisible(false);
  };

  const handleEssentialsOnly = () => {
    localStorage.setItem("cookieConsent", "essential");
    setIsVisible(false);
  };

  const handleOpenDialog = () => {
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
  };

  const handleOpenPolicyDialog = () => {
    setIsPolicyDialogOpen(true);
  };

  const handleClosePolicyDialog = () => {
    setIsPolicyDialogOpen(false);
  };

  // Function to reset cookie consent (for testing)
  const resetCookieConsent = () => {
    localStorage.removeItem("cookieConsent");
    setIsVisible(true);
  };

  // If not visible, show only the reset button for testing
//   if (!isVisible) {
//     return (
//       <Box sx={{ position: "fixed", bottom: 16, right: 16, zIndex: 1000 }}>
//         <Button
//           variant="outlined"
//           size="small"
//           color="primary"
//           onClick={resetCookieConsent}
//         >
//           Reset Cookie Consent
//         </Button>
//       </Box>
//     );
//   }

  if (!isVisible) return null;

  return (
    <Paper className="cookie-consent-banner" elevation={3}>
      <Box className="cookie-consent-container">
        <Box className="cookie-consent-content">
          <Typography variant="h5" className="cookie-consent-title">
            {COOKIE_CONSENT.title}
          </Typography>
          <Typography variant="body1" className="cookie-consent-text">
            {COOKIE_CONSENT.mainText}
          </Typography>
          <Typography variant="body1" className="cookie-consent-text">
            {COOKIE_CONSENT.consentText}
          </Typography>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <Box>
              <Typography variant="body1" className="cookie-consent-text">
                {COOKIE_CONSENT.policyText.split("cookies policy")[0]}{" "}
                <Button
                  onClick={handleOpenPolicyDialog}
                  sx={{
                    fontFamily: "Poppins",
                    fontWeight: "700 !important",
                    // fontSize: "20px",
                    lineHeight: "30px",
                    letterSpacing: 0,
                    textAlign: "justify",
                    verticalAlign: "middle",
                    textDecoration: "underline",
                    textDecorationStyle: "solid",
                    textDecorationOffset: 0,
                    textDecorationThickness: 0,
                    textDecorationSkipInk: "auto",
                    color: "#222222",
                    textTransform: "none",
                  }}
                >
                  cookies policy
                </Button>
                {COOKIE_CONSENT.policyText.split("cookies policy")[1]}
              </Typography>
              <Typography variant="body1" className="cookie-consent-text">
                {
                  COOKIE_CONSENT.preferenceCenterText.split(
                    "Cookies Preference Center"
                  )[0]
                }{" "}
                <Button
                  onClick={handleOpenDialog}
                  sx={{
                    fontFamily: "Poppins",
                    fontWeight: 700,
                    // fontSize: "20px",
                    lineHeight: "30px",
                    letterSpacing: 0,
                    textAlign: "justify",
                    verticalAlign: "middle",
                    textDecoration: "underline",
                    textDecorationStyle: "solid",
                    textDecorationOffset: 0,
                    textDecorationThickness: 0,
                    textDecorationSkipInk: "auto",
                    color: "#222222",
                    textTransform: "none",
                  }}
                >
                  Cookies Preference Center
                </Button>{" "}
                {
                  COOKIE_CONSENT.preferenceCenterText.split(
                    "Cookies Preference Center"
                  )[1]
                }
              </Typography>
            </Box>
            <Box
              className="cookie-consent-buttons"
              sx={{
                display: "flex",
                gap: "43px",
                justifyContent: "flex-end",
                mt: 2,
              }}
            >
              <Button onClick={handleAcceptAll}>
                {COOKIE_CONSENT.buttons.acceptAll}
              </Button>
              <Button onClick={handleEssentialsOnly}>
                {COOKIE_CONSENT.buttons.essentialsOnly}
              </Button>
            </Box>
          </Box>
        </Box>
      </Box>
      <CookieDialog open={isDialogOpen} onClose={handleCloseDialog} />
      <CookiePolicyDialog
        open={isPolicyDialogOpen}
        onClose={handleClosePolicyDialog}
        onOpenPreferences={handleOpenDialog}
      />
    </Paper>
  );
};

export default CookieConsent;
