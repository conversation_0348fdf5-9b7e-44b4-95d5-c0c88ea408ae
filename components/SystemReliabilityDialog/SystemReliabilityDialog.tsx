"use client";

import type React from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  Box,
  Typography,
  IconButton,
} from "@mui/material";
import Image from "next/image";
import "./SystemReliabilityDialog.scss";
import { SystemReliablityIcon, SystemReliablityIconButton } from "@/public";

interface SystemReliabilityDialogProps {
  open: boolean;
  onClose: () => void;
}

const SystemReliabilityDialog: React.FC<SystemReliabilityDialogProps> = ({
  open,
  onClose,
}) => {
  const sections = [
    {
      title: "System Function Testing",
      description:
        "Ensuring comprehensive performance and validation of both hardware and software interactions in your design. Verifying that system design performance as expected under various environmental conditions and use cases.",
    },
    {
      title: "Product Reliability Assessments",
      description:
        "Evaluating the long-term durability and operational stability of your embedded system in real-world environments. Stress testing under extreme conditions to simulate and identify potential failure points.",
    },
    {
      title: "EMI & EMC Testing",
      description:
        "Our team ensure that the developed embedded system does not emit harmful electromagnetic interference (EMI) that could affect other devices or systems and validating that the system is immune to external electromagnetic disturbances, maintaining stable and reliable operation in field environments.",
    },
    {
      title: "Safety Assessment",
      description:
        "Executing comprehensive safety analysis to verify whether the product design meet international standards to ensuring your product is safe for use in the target market. Identifying and mitigating potential hazards, including electrical safety, thermal management, and operational safety in normal and fault conditions.",
    },
    {
      title: "Compliance Testing",
      description:
        "We ensure full compliance with Global Standards by ensuring your product meets key industry certifications and regulatory requirements, including ISO, IEC, UL, CE, FCC and RoHS. We ensure your product adheres to safety, electromagnetic compatibility, and environmental standards.",
    },
    {
      title: "Reliability Engineering & Support",
      description:
        "Failure Mode and Effects Analysis (FMEA) to anticipate and mitigate potential issues. Ongoing post-production support for system updates, system failures, and continuous reliability monitoring in the field.",
    },
  ];

  return (
    <Dialog
      open={open}
      onClose={onClose}
      className="system-reliability-dialog"
      PaperProps={{
        className: "dialog-paper",
      }}
      //   maxWidth={false}
    >
      <DialogTitle className="dialog-title">
        <Box className="title-content">
          <Image
            src={SystemReliablityIcon}
            alt="system"
            width={42}
            height={45}
          />
          <Typography className="title">System Reliability Testing</Typography>
        </Box>
        <IconButton onClick={onClose}>
          <Image src={SystemReliablityIconButton} alt="high" />
        </IconButton>
      </DialogTitle>

      <DialogContent className="dialog-content">
        <div className="content-grid">
          {sections.map((section, index) => (
            <div key={index} className="section">
              <h3 className="section-title">{section.title}</h3>
              <p className="section-description">{section.description}</p>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SystemReliabilityDialog;
