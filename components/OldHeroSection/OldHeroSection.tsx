"use client";
import React from "react";
import "./OldHeroSection.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import { EffectFade, Autoplay, Navigation, Pagination } from "swiper/modules";
// Import Swiper styles
import "swiper/css";
import "swiper/css/effect-fade";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { heroSlides, heroSectionContent } from "@/constant";
import { heroCarousal_1 } from "@/public/index";
import { hero_bg_ellipse, Ellipse_3, Rectangle } from "@/public/index";
const OldHeroSection = () => {
  return (
    <Box className="hero-section-container">
      <section className="swiper-section-container">
        <Swiper
          modules={[EffectFade, Autoplay, Navigation, Pagination]}
          effect="fade"
          autoplay={{
            delay: 3000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }}
          // navigation={true}
          pagination={{ clickable: true }}
          speed={2000}
          loop={true}
          className="swiper-container"
        >
          {heroSlides.slice(0, 3).map((slide) => (
            <SwiperSlide key={slide.id}>
              <div
                className="slide-image-container"
                style={{ position: "relative", width: "100%", height: "100%" }}
              >
                {/* Ellipse background for first slide, styled like mechanical page */}
                {slide.id === 1 && (
                  <Box className="ellipse-container">
                    <Image
                      src={hero_bg_ellipse}
                      alt="Ellipse"
                      className="ellipse-image"
                    />
                  </Box>
                )}
                <Image
                  src={slide.image}
                  alt="Hero Carousel"
                  fill
                  style={{ objectFit: "cover", zIndex: 1 }}
                  priority={slide.id === 1}
                />
                <div className="overlay"></div>
                {slide.id === 3 && (
                  <Box className="hero-section-content">
                    <Typography className="hero-section-text">
                      Gearup your Innovation Progress
                    </Typography>
                  </Box>
                )}
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </section>

      {/* <section
        className="hero-section"
        style={{
          position: "relative",
          width: "100%",
          height: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Image
          src={Ellipse_2}
          alt="Hero Background Ellipse"
          fill
          style={{ objectFit: "cover", zIndex: 0 }}
          className="hero-bg-ellipse"
          priority
        />

        <Image
          src={Ellipse_3}
          alt="Hero Background Ellipse"
          fill
          style={{ objectFit: "cover", zIndex: 0 }}
          className="hero-bg-ellipse"
          priority
        />

        <Image
          src={Rectangle}
          alt="Hero Background Rectangle"
          fill
          style={{ objectFit: "cover", zIndex: 0 }}
          className="hero-bg-rectangle"
          priority
        />

        <Box
          className="hero-content"
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 2,
            color: "#fff",
            textAlign: "center",
          }}
        >
          <Image
            src={heroCarousal_1}
            alt="Hero Logo"
            className="hero-logo"
            priority
          />
        </Box>
      </section> */}
    </Box>
  );
};

export default OldHeroSection;
