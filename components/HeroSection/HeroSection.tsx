import Image from "next/image";
import { Noise, hero_bg_ellipse } from "@/public/index";
import { Box, Typography } from "@mui/material";
import "./HeroSection.scss";

const HeroSection = () => {
  return (
    <section
      className="hero-section"
      style={{
        position: "relative",
        width: "100%",
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Image
        src={Noise}
        alt="Noise Background"
        fill
        style={{ objectFit: "cover", zIndex: 1 }}
        className="noise-background"
        priority
      />

      <Image
        src={hero_bg_ellipse}
        alt="Hero Background Ellipse"
        fill
        style={{ objectFit: "cover", zIndex: 0 }}
        className="hero-bg-ellipse"
        priority
      />
      

      <Box
        className="hero-content"
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          zIndex: 2,
          color: "#fff",
          textAlign: "center",
        }}
      >
        <Typography variant="h1" className="hero-title">
          Aadvik
        </Typography>
        <Typography className="hero-subtitle">
          We Design remarkable Engineering Products
        </Typography>
      </Box>
    </section>
  );
};

export default HeroSection;
