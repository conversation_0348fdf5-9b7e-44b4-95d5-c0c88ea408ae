.hero-section {
    .noise-background {
        opacity: 50%;
        scale: tile;
    }

    .hero-content {
        .hero-title {
            opacity: 50%;
            font-family: Inter !important;
            font-weight: 700;
            font-size: 251.87px;

            letter-spacing: 0%;
            text-align: center;
            background: linear-gradient(180deg, #0F435D 27.88%, #84BDDA 100%);
            background-clip: text;
            -webkit-text-fill-color: transparent;

        }

        .hero-subtitle {
            font-family: Inter !important;
            font-weight: 600;
            font-size: 24px;
            leading-trim: Cap height;
            line-height: 47.91px;
            letter-spacing: 0%;
            text-align: center;
            color: #D8D5D5;

        }
    }
}