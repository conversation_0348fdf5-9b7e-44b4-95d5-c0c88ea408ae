.communication-testing-dialog {
  .dialog-paper {
    background-color: #021f2e;
    color: white;
    max-width: 1327px;
    // width: 90vw;
    min-height: 80vh;
    border-radius: 10px;
    padding: 0;
  }

  .dialog-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 40px 24px 75px;
    border-bottom: #d77d46 0.5px solid;
    margin: 0;

    .title-content {
      display: flex;
      align-items: center;
      gap: 24px;

      .title {
        font-family: Montserrat !important;
        font-weight: 700 !important;
        font-style: Bold;
        font-size: 36px !important;
        letter-spacing: 0%;
        vertical-align: middle;
      }
    }
  }

  .communication-divider {
    color:red;
  }

  .dialog-content {
    margin-top: 53px;
    padding-left: 75px;
    padding-right: 63px;
    background-color: #021f2e;

    .content-layout {
      display: flex;
      gap: 32px;

      @media (max-width: 1024px) {
        flex-direction: column;
        gap: 24px;
      }
    }

    .left-description {
      flex: 0 0 300px;

      @media (max-width: 1024px) {
        flex: none;
      }

      .description-text {
        font-family: Poppins !important;
        font-weight: 400;
        font-style: Regular;
        font-size: 13px;
        letter-spacing: 0%;

        margin-top: 20px;

        &:first-child {
          margin-top: 0;
        }
      }
    }

    .content-grid {
      flex: 1;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 32px;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 24px;
      }
    }

    .section {
      .section-title {
        font-family: Poppins !important;
        font-weight: 600;
        font-style: SemiBold;
        font-size: 13px;
        line-height: 24.5px;
        letter-spacing: 0%;
        vertical-align: middle;

        margin-bottom: 17px;
      }

      .section-description {
        max-width: 512px;
        font-family: Poppins !important;
        font-weight: 400;
        font-style: Regular;
        font-size: 13px;
        letter-spacing: 0%;
        vertical-align: middle;

        margin-bottom: 12px;
      }

      .solution-button {
        background-color: #4a90e2;
        color: white;
        border: none;
        padding: 6px 16px;
        border-radius: 4px;
        font-family: Poppins !important;
        font-weight: 600;
        font-size: 12px;
        cursor: pointer;
        text-transform: uppercase;

        &:hover {
          background-color: #357abd;
        }
      }
    }
  }
}
