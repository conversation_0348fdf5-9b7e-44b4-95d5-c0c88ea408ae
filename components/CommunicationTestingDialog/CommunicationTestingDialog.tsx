"use client";

import type React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Box,
  Typography,
  IconButton,
  Divider,
} from "@mui/material";
import Image from "next/image";
import "./CommunicationTestingDialog.scss";
import { SystemReliablityIcon, SystemReliablityIconButton } from "@/public";

interface DialogSection {
  title: string;
  description: string;
  hasButton?: boolean;
  buttonText?: string;
}

interface CommunicationTestingDialogProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  leftDescriptions?: string[];
  sections?: DialogSection[];
}

const CommunicationTestingDialog: React.FC<CommunicationTestingDialogProps> = ({
  open,
  onClose,
  title = "Communication Testing",
  leftDescriptions = ["Default description for IoT systems."],
  sections = [{ title: "Default", description: "Default content." }],
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      className="communication-testing-dialog"
      slotProps={{
        paper: {
          className: "dialog-paper",
        },
      }}
    >
      <DialogTitle className="dialog-title">
        <Box className="title-content">
          <Image
            src={SystemReliablityIcon}
            alt="dialog icon"
            width={42}
            height={45}
          />
          <Typography className="title">{title}</Typography>
        </Box>
        <IconButton onClick={onClose}>
          <Image src={SystemReliablityIconButton} alt="close" />
        </IconButton>
        
      </DialogTitle>

      <DialogContent className="dialog-content">
        <div className="content-layout">
          <div className="left-description">
            {leftDescriptions.map((description, index) => (
              <Typography key={index} className="description-text">
                {description}
              </Typography>
            ))}
          </div>
          <div className="content-grid">
            {sections.map((section, index) => (
              <div key={index} className="section">
                <h3 className="section-title">{section.title}</h3>
                <p className="section-description">{section.description}</p>
              </div>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CommunicationTestingDialog;
