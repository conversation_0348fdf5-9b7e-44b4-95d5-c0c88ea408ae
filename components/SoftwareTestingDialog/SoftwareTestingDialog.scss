.software-testing-dialog {
  .dialog-paper {
    background-color: #021f2e;
    color: white;
    min-width: 1327px;
    min-height: 80vh;
    width: 90vw;
    border-radius: 10px;
    padding: 0;
  }

  .dialog-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 75px;
    border-bottom: #d77d46 0.5px solid;
    margin: 0;

    .title-content {
      display: flex;
      align-items: center;
      gap: 24px;

      .title {
        font-family: Montserrat !important;
        font-weight: 700 !important;
        font-style: Bold;
        font-size: 36px !important;
        letter-spacing: 0%;
        vertical-align: middle;
      }
    }
  }

  .dialog-content {
    padding-left: 75px;
    background-color: #021f2e;
    margin-top: 58px;
    .content-layout {
      display: flex;
      gap: 116px;

      @media (max-width: 1024px) {
        flex-direction: column;
        gap: 32px;
      }
    }

    .left-description {
      flex: 0 0 400px;

      @media (max-width: 1024px) {
        flex: none;
      }

      .description-text {
        max-width: 348px;
        font-family: Poppins !important;
        font-weight: 400;
        font-size: 13px;
        line-height: 22px;
        color: #ffffff;
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .right-content {
      flex: 1;

      .process-title {
        font-family: Poppins !important;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        color: #ffffff;
        margin-bottom: 24px;
        margin-top: 0;
      }

      .process-list {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .process-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;

        .process-icon {
          flex-shrink: 0;
          margin-top: 2px;
        }

        .process-text {
          font-family: Poppins !important;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          color: #ffffff;
          margin: 0;
          flex: 1;
        }
      }
    }
  }
}
