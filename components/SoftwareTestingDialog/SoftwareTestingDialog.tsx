"use client";

import type React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Box,
  Typography,
  IconButton,
} from "@mui/material";
import Image from "next/image";
import "./SoftwareTestingDialog.scss";
import { SystemReliablityIcon, SystemReliablityIconButton } from "@/public";

interface SoftwareTestingDialogProps {
  open: boolean;
  onClose: () => void;
  title: string;
  leftDescriptions: string[];
  rightSideList: string[];
}

const SoftwareTestingDialog: React.FC<SoftwareTestingDialogProps> = ({
  open,
  onClose,
  title,
  leftDescriptions,
  rightSideList,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      className="software-testing-dialog"
      slotProps={{
        paper: {
          className: "dialog-paper",
        },
      }}
    >
      <DialogTitle className="dialog-title">
        <Box className="title-content">
          <Image src={SystemReliablityIcon} alt="dialog icon" width={42} height={45} />
          <Typography className="title">{title}</Typography>
        </Box>
        <IconButton onClick={onClose}>
          <Image src={SystemReliablityIconButton} alt="close" />
        </IconButton>
      </DialogTitle>

      <DialogContent className="dialog-content">
        <div className="content-layout">
          <div className="left-description">
            {leftDescriptions.map((description, index) => (
              <p key={index} className="description-text">{description}</p>
            ))}
          </div>
          <div className="right-content">
            <h3 className="process-title">Our General standard {title} Process involve -</h3>
            <div className="process-list">
              {rightSideList.map((step, index) => (
                <div key={index} className="process-item">
                  <div className="process-icon">
                    <Image src={SystemReliablityIcon} alt="step icon" width={20} height={20} />
                  </div>
                  <p className="process-text">{step}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SoftwareTestingDialog;