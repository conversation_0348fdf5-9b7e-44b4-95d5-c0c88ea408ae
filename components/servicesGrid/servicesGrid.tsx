"use client";

import React from "react";
import "./serviceNavDropdown.scss";
import Image from "next/image";
import { Box, Typography } from "@mui/material";
import { useRouter, usePathname, useSearchParams } from "next/navigation";

interface ServiceItem {
  title: string;
  items: { name: string; route?: string }[];
  icon: any;
  route?: string;
}

interface ServiceNavDropdownProps {
  data: ServiceItem[];
  onClose?: () => void;
  onNavigate?: (tab: string) => void;
}

const ServiceNavDropdown: React.FC<ServiceNavDropdownProps> = ({
  data,
  onClose,
  onNavigate,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-+|-+$/g, "");
  };

  const scrollToSection = (section: string) => {
    const element = document.getElementById(section);
    if (element) {
      const offset = 80; // Adjust based on your fixed header height
      const elementPosition =
        element.getBoundingClientRect().top + window.pageYOffset;
      window.scrollTo({
        top: elementPosition - offset,
        behavior: "smooth",
      });
    }
  };

  const handleClick = (route: string, event: React.MouseEvent) => {
    event.preventDefault(); // Prevent default anchor behavior
    const element = event.currentTarget as HTMLElement;
    element.classList.add("clicked");

    setTimeout(() => {
      // Parse the route to extract path and query parameters
      const url = new URL(route, "http://localhost"); // Use a dummy base URL
      const path = url.pathname;
      const section = url.searchParams.get("section");

      if (path === pathname && section) {
        // Same page, different section: scroll directly
        scrollToSection(section);
      } else {
        // Different page or no section: navigate with router
        router.push(route, { scroll: false });
      }
      if (onNavigate) {
        if (route.includes("services")) onNavigate("Services");
        else if (route.includes("industries")) onNavigate("Industries");
      }
      if (onClose) {
        onClose();
      }
    }, 300); // Match with animation duration
  };

  return (
    <Box className="service-nav-dropdown">
      <Box className="service-nav-header">
        {data.map((service, index) => (
          <Box key={index}>
            <Box className="service-card">
              <Box
                className="service-header"
                onClick={(e) => service.route && handleClick(service.route, e)}
                style={{ cursor: "pointer" }}
              >
                <Typography variant="h6" className="service-title">
                  {service.title}
                </Typography>
              </Box>
              <ul className="service-items">
                {service.items.map((item, idx) => (
                  <li
                    key={idx}
                    className="service-item"
                    onClick={(e) =>
                      item.route
                        ? handleClick(item.route, e)
                        : handleClick(
                            `${service.route}/${generateSlug(item.name)}`,
                            e
                          )
                    }
                    style={{ cursor: "pointer" }}
                  >
                    ^ {item.name}
                  </li>
                ))}
              </ul>
              {service.icon && (
                <Box className="icon-wrapper">
                  <Image
                    src={service.icon}
                    alt={service.title}
                    width={140}
                    height={140}
                  />
                </Box>
              )}
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default ServiceNavDropdown;
