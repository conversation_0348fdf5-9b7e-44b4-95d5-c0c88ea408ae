.industries-nav-dropdown {
  .service-nav-header {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    position: relative;
    // border: 1px solid red;
    border-radius: 8px;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 32.33%;
      left: 33.33%;
      transform: translate(-50%, -50%) rotate(90deg);
      width: 23px;
      height: 26px;
      background: #d77d46;
      z-index: 10;
      clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
    }

    &::after {
      content: "";
      position: absolute;
      top: 64.5%;
      left: 66.66%;
      transform: translate(-50%, -50%) rotate(90deg);
      width: 23px;
      height: 26px;
      background: #d77d46;
      z-index: 10;
      border-radius: 1px;
      clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
    }

    > div {
      border-right: 1px solid #d77d46;
      border-bottom: 1px solid #d77d46;

      &:nth-child(3n) {
        border-right: none;
      }
      &:nth-last-child(-n + 3) {
        border-bottom: none;
      }
    }
  }

  .service-card {
    // OLD STYLING
    // width: 466px;
    // height: 340px;
    padding: 20px 0px 20px 10px;
    min-width: 400px;
    height: 100%;
    max-height: 400px;
    position: relative;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: none;
    background-color: #f6f6f6;
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;

    &:hover {
      background-color: #d77d4629;
    }

    .service-header {
      position: relative;
      z-index: 2;
      margin-bottom: 12px;
      transform-origin: center;
      transition: all 0.3s ease-in-out;

      .service-title {
        color: #d77d46;
        font-family: Inter !important;
        font-weight: 700;
        font-style: Bold;
        font-size: 16px;
        letter-spacing: 0%;

        margin-left: 18px;
        cursor: pointer;
      }

      &:hover {
        transform: translateY(-2px);
      }

      &.clicked {
        animation: clickEffect 0.3s ease-in-out;
      }
    }

    .service-items {
      list-style: none;
      padding: 0;
      margin-left: 18px;
      position: relative;
      z-index: 2;

      .service-item {
        font-family: Montserrat !important;
        font-weight: 400;
        font-style: Regular;
        font-size: 11px;
        line-height: 16px;
        letter-spacing: 0%;

        color: #021f2e;
        cursor: pointer;

        &:hover {
          color: #d77d46;
        }
      }
    }

    .icon-wrapper {
      position: absolute;
      bottom: 20px;
      right: 20px;
      // opacity: 1;
      transform: translate(5px, 5px);
      transition: all 0.3s ease;
      z-index: 1;
      // width: 120px;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;

      img {
        width: 97px;
        height: 97px;
        object-fit: contain;
      }
    }
  }
}

@keyframes clickEffect {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

// Responsive adjustments for diamond connectors
@media screen and (min-width: 1200px) and (max-width: 1600px) {
  .service-nav-dropdown .service-nav-header {
    &::before {
      left: 33.33%;
    }
    &::after {
      left: 66.66%;
    }
  }
}

@media screen and (max-width: 1199px) {
  .service-nav-dropdown .service-nav-header {
    grid-template-columns: repeat(2, 1fr);

    // Adjust diamond connector for 2 columns
    &::before {
      left: 50%;
    }

    &::after {
      display: none; // Hide second connector for 2 columns
    }

    > div {
      // Reset 3-column rules
      &:nth-child(3n) {
        border-right: 1px solid #d77d4629;
      }

      // Apply 2-column rules
      &:nth-child(2n) {
        border-right: none;
      }

      &:nth-last-child(-n + 2) {
        border-bottom: none;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .service-nav-dropdown .service-nav-header {
    display: flex !important;
    flex-direction: column;

    // Hide diamond connectors on mobile
    &::before,
    &::after {
      display: none;
    }

    > div {
      border-right: none;
      border-bottom: 1px solid #d77d4629;

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

// .service-nav-dropdown {
//   .service-nav-header {
//     display: grid;
//     grid-template-columns: repeat(3, 1fr);

//     // &>div:nth-child(even) .service-card {
//     //     background: transparent !important;
//     // }
//   }

//   .service-card {
//     // OLD STYLING
//     // width: 466px;
//     // height: 340px;
//     padding: 20px 0px 20px 13px;
//     min-width: 400px;
//     height: 100%;
//     max-height: 400px;
//     position: relative;
//     overflow: hidden;
//     // background-image: url('/assets/images/png/grid_bg.png');
//     background-size: cover;
//     background-position: center;
//     background-repeat: no-repeat;
//     // border-radius: 16px;
//     // background-color: #021f2e;
//     border: 1px solid #d77d4629;
//     // box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
//     transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;

//     &:hover {
//       background-color: #d77d4629;
//     }

//     .service-header {
//       position: relative;
//       z-index: 2;
//       margin-bottom: 12px;
//       transform-origin: center;
//       transition: all 0.3s ease-in-out;

//       .service-title {
//         color: #8cffe4;
//         font-family: inter !important;
//         font-weight: 500;
//         // font-size: 22px;
//         font-size: 18px;
//         line-height: 30px;
//         letter-spacing: 0;
//         margin-left: 18px;
//         cursor: pointer;
//         color: #d77d46;
//       }

//       &:hover {
//         transform: translateY(-2px);
//       }

//       &.clicked {
//         animation: clickEffect 0.3s ease-in-out;
//       }
//     }

//     .service-items {
//       list-style: none;
//       padding: 0;
//       margin: 0;
//       position: relative;
//       z-index: 2;

//       .service-item {
//         font-family: Montserrat !important;
//         font-weight: 400;
//         font-size: 14px;
//         line-height: 20px;
//         letter-spacing: 0%;
//         color: #021f2e;
//         cursor: pointer;

//         &:hover {
//           color: #d77d46;
//         }

//         &::before {
//           content: "^";
//           color: #ffffff;
//           margin-right: 8px;
//         }
//       }
//     }

//     .icon-wrapper {
//       position: absolute;
//       bottom: 20px;
//       right: 20px;
//       opacity: 1;
//       transform: translate(5px, 5px);
//       transition: all 0.3s ease;
//       z-index: 1;
//       width: 120px;
//       height: 120px;
//       display: flex;
//       align-items: center;
//       justify-content: center;
//       // background: #093246;
//       border-radius: 4px;

//       img {
//         width: 70%;
//         height: 70%;
//         object-fit: contain;
//         opacity: 0.8;
//       }
//     }
//   }
// }

// @keyframes clickEffect {
//   0% {
//     transform: scale(1);
//     opacity: 1;
//   }

//   50% {
//     transform: scale(0.95);
//     opacity: 0.8;
//   }

//   100% {
//     transform: scale(1);
//     opacity: 0;
//   }
// }

// @media screen and (min-width: 1200px) and (max-width: 1600px) {
// }

// @media screen and (max-width: 1199px) {
// }

// @media screen and (max-width: 768px) {
//   .service-nav-header {
//     display: flex !important;
//     flex-wrap: wrap;
//   }
// }
