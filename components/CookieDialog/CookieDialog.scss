.cookie-dialog {
    .MuiDialog-paper {
        background: #D9D9D9;
        border-radius: 20px;
        width: 100% !important;
        max-width: 90% !important;
        margin: 16px;

        @media (min-width: 768px) {
            max-width: 720px !important;
            margin: 20px;
        }

        @media (min-width: 1200px) {
            max-width: 1000px !important;
        }

        @media (min-width: 1400px) {
            max-width: 1200px !important;
        }

        @media (min-width: 1600px) {
            max-width: 1400px !important;
        }
    }

    .cookie-dialog-title {
        font-family: Poppins;
        font-weight: 500;
        font-size: 20px;
        line-height: 1.2;
        letter-spacing: 0%;
        padding: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 2px solid #000000;

        @media (min-width: 768px) {
            font-size: 24px;
            padding: 20px 24px;
        }

        @media (min-width: 1200px) {
            font-size: 28px;
            padding: 24px 32px;
        }

        @media (min-width: 1400px) {
            font-size: 32px;
            padding: 28px 40px;
        }

        @media (min-width: 1600px) {
            font-size: 36px;
            padding: 32px 48px;
        }
    }

    .cookie-dialog-content {
        padding: 0 !important;

        .cookie-dialog-stack {
            .cookie-dialog-box {
                padding: 16px;
                border-bottom: 1px solid #1D1B20;

                @media (min-width: 768px) {
                    padding: 24px 32px;
                }

                @media (min-width: 1200px) {
                    padding: 28px 40px;
                }

                @media (min-width: 1400px) {
                    padding: 32px 48px;
                }

                @media (min-width: 1600px) {
                    padding: 36px 56px;
                }

                .cookie-dialog-sub-title {
                    font-family: Poppins;
                    font-weight: 500;
                    font-size: 16px;
                    line-height: 1.4;
                    margin-bottom: 8px;

                    @media (min-width: 768px) {
                        font-size: 18px;
                        margin-bottom: 12px;
                    }

                    @media (min-width: 1200px) {
                        font-size: 20px;
                        margin-bottom: 16px;
                    }

                    @media (min-width: 1400px) {
                        font-size: 22px;
                    }

                    @media (min-width: 1600px) {
                        font-size: 24px;
                    }
                }

                .cookie-dialog-text {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 1.5;
                    color: #444;

                    @media (min-width: 768px) {
                        font-size: 15px;
                    }

                    @media (min-width: 1200px) {
                        font-size: 16px;
                    }

                    @media (min-width: 1400px) {
                        font-size: 17px;
                    }

                    @media (min-width: 1600px) {
                        font-size: 18px;
                    }
                }

                .MuiSwitch-root {
                    width: 45px;
                    height: 32px;
                    padding: 5px;
                    border-radius: 20px;

                    .MuiSwitch-switchBase {
                        padding: 8px;
                        color: #FFFFFF;

                        &.Mui-checked {
                            transform: translateX(13px);
                        }
                    }

                    .MuiSwitch-thumb {
                        border-radius: 20px;
                        padding: 2px;
                        width: 16px;
                        height: 16px;
                    }

                    .MuiSwitch-track {
                        background-color: #6750A4;
                        border-radius: 20px;
                    }

                    .Mui-checked+.MuiSwitch-track {
                        background-color: #6750A4;
                        opacity: 1;
                    }

                    .Mui-checked .MuiSwitch-thumb {
                        background-color: #FFFFFF;
                    }
                }
            }
        }
    }

    .cookie-dialog-actions {
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 16px;

        @media (min-width: 768px) {
            padding: 24px 32px;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            gap: 24px;
        }

        @media (min-width: 1200px) {
            padding: 28px 40px;
            gap: 32px;
        }

        @media (min-width: 1400px) {
            padding: 32px 48px;
        }

        @media (min-width: 1600px) {
            padding: 36px 56px;
        }

        .cookie-dialog-footer {
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex: 1;

            @media (min-width: 768px) {
                gap: 12px;
            }

            .cookie-dialog-footer-text {
                font-family: Poppins;
                font-size: 14px;
                line-height: 1.5;
                color: #444;
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                gap: 4px;

                @media (min-width: 768px) {
                    font-size: 15px;
                }

                @media (min-width: 1200px) {
                    font-size: 16px;
                }

                @media (min-width: 1400px) {
                    font-size: 17px;
                }

                @media (min-width: 1600px) {
                    font-size: 18px;
                    line-height: 30px;
                }

                button {
                    display: inline;
                    height: auto;
                    min-height: auto;
                    vertical-align: baseline;
                }
            }
        }

        .cookie-dialog-save-button {
            font-family: Poppins;
            font-weight: 600;
            font-size: 14px;
            line-height: 1.4;
            text-transform: none;
            border-radius: 30px;
            background: #4988C8;
            padding: 12px 24px;
            width: 100%;
            white-space: nowrap;
            height: fit-content;
            min-width: fit-content;

            @media (min-width: 768px) {
                font-size: 15px;
                padding: 14px 32px;
                width: auto;
            }

            @media (min-width: 1200px) {
                font-size: 16px;
                padding: 16px 40px;
            }

            @media (min-width: 1400px) {
                font-size: 17px;
                padding: 18px 48px;
            }

            @media (min-width: 1600px) {
                font-size: 18px;
                padding: 20px 56px;
            }

            &:hover {
                background: darken(#4988C8, 10%);
            }
        }
    }
}