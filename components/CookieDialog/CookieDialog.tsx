import "./CookieDialog.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Switch,
  Button,
  Divider,
  FormControlLabel,
  Stack,
  Checkbox,
} from "@mui/material";
import { useState } from "react";
import { closeIcon } from "@/public/index";
import Image from "next/image";
import { COOKIE_DIALOG } from "@/constant";

const CookieDialog = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) => {
  const [cookies, setCookies] = useState({
    performance: true,
    functional: true,
    advertising: true,
  });

  type CookieTypes = "performance" | "functional" | "advertising";

  const handleToggle = (name: CookieTypes) => {
    setCookies((prev) => ({
      ...prev,
      [name]: !prev[name],
    }));
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      className="cookie-dialog"
    >
      <DialogTitle className="cookie-dialog-title">
        {COOKIE_DIALOG.title}
        <Box
          className="cookie-dialog-close-icon"
          onClick={onClose}
          sx={{ cursor: "pointer" }}
        >
          <Image src={closeIcon} alt="Close" />
        </Box>
      </DialogTitle>

      <DialogContent className="cookie-dialog-content">
        <Stack className="cookie-dialog-stack">
          {/* Strictly Necessary Cookies */}
          <Box className="cookie-dialog-box">
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                className="cookie-dialog-sub-title"
                variant="subtitle1"
              >
                {COOKIE_DIALOG.sections.necessary.title}
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography color="success.main" fontWeight="bold">
                  {COOKIE_DIALOG.sections.necessary.status}
                </Typography>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked
                      disabled
                      sx={{
                        color: "#009951",
                        "&.Mui-checked": {
                          color: "#009951",
                        },
                      }}
                    />
                  }
                  label=""
                  sx={{ m: 0 }}
                />
              </Box>
            </Box>
            <Typography className="cookie-dialog-text" variant="body2" mt={1}>
              {COOKIE_DIALOG.sections.necessary.description}
            </Typography>
          </Box>

          {/* Performance Cookies */}
          <Box className="cookie-dialog-box">
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                className="cookie-dialog-sub-title"
                variant="subtitle1"
              >
                {COOKIE_DIALOG.sections.performance.title}
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography>
                  {COOKIE_DIALOG.sections.performance.status}
                </Typography>
                <Switch
                  checked={cookies.performance}
                  onChange={() => handleToggle("performance")}
                />
              </Box>
            </Box>
            <Typography variant="body2" mt={1} className="cookie-dialog-text">
              {COOKIE_DIALOG.sections.performance.description}
            </Typography>
          </Box>

          {/* Functional Cookies */}
          <Box className="cookie-dialog-box">
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                className="cookie-dialog-sub-title"
                variant="subtitle1"
              >
                {COOKIE_DIALOG.sections.functional.title}
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography>
                  {COOKIE_DIALOG.sections.functional.status}
                </Typography>
                <Switch
                  checked={cookies.functional}
                  onChange={() => handleToggle("functional")}
                />
              </Box>
            </Box>
            <Typography variant="body2" mt={1} className="cookie-dialog-text">
              {COOKIE_DIALOG.sections.functional.description}
            </Typography>
          </Box>

          {/* Advertising Cookies */}
          <Box className="cookie-dialog-box">
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                className="cookie-dialog-sub-title"
                variant="subtitle1"
              >
                {COOKIE_DIALOG.sections.advertising.title}
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography>
                  {COOKIE_DIALOG.sections.advertising.status}
                </Typography>
                <Switch
                  checked={cookies.advertising}
                  onChange={() => handleToggle("advertising")}
                />
              </Box>
            </Box>
            <Typography variant="body2" mt={1} className="cookie-dialog-text">
              {COOKIE_DIALOG.sections.advertising.description}
            </Typography>
          </Box>
        </Stack>
      </DialogContent>

      <DialogActions className="cookie-dialog-actions">
        <Box className="cookie-dialog-footer">
          <Typography variant="body2" className="cookie-dialog-footer-text">
            See our{" "}
            <Button
              onClick={() =>
                window.open("/privacy-policy", "_blank", "noopener,noreferrer")
              }
              sx={{
                fontFamily: "Poppins",
                fontWeight: "700 !important",
                lineHeight: "30px",
                letterSpacing: 0,
                textAlign: "justify",
                verticalAlign: "middle",
                textDecoration: "underline",
                textDecorationStyle: "solid",
                textDecorationOffset: 0,
                textDecorationThickness: 0,
                textDecorationSkipInk: "auto",
                color: "#222222",
                textTransform: "none",
                padding: 0,
                minWidth: "auto",
                "&:hover": {
                  backgroundColor: "transparent",
                },
              }}
            >
              privacy policy
            </Button>{" "}
            for more information on the use of your personal data.
          </Typography>
          <Typography variant="body2" className="cookie-dialog-footer-text">
            Please refer our{" "}
            <Button
              onClick={() =>
                window.open("/cookies-policy", "_blank", "noopener,noreferrer")
              }
              sx={{
                fontFamily: "Poppins",
                fontWeight: "700 !important",
                lineHeight: "30px",
                letterSpacing: 0,
                textAlign: "justify",
                verticalAlign: "middle",
                textDecoration: "underline",
                textDecorationStyle: "solid",
                textDecorationOffset: 0,
                textDecorationThickness: 0,
                textDecorationSkipInk: "auto",
                color: "#222222",
                textTransform: "none",
                padding: 0,
                minWidth: "auto",
                "&:hover": {
                  backgroundColor: "transparent",
                },
              }}
            >
              cookies policy
            </Button>{" "}
            for more information on the usage of cookies.
          </Typography>
        </Box>

        <Button
          className="cookie-dialog-save-button"
          variant="contained"
          color="primary"
          sx={{ borderRadius: "24px", px: 4 }}
          onClick={onClose}
        >
          {COOKIE_DIALOG.footer.saveButton}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CookieDialog;
