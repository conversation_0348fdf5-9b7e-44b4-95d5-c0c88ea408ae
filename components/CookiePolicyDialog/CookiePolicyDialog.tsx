import React from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogContent,
  DialogTitle,
  List,
  ListItem,
  ListItemText,
  IconButton,
  useTheme,
} from "@mui/material";
import { COOKIE_POLICY } from "@/constant";

interface CookiePolicyDialogProps {
  open: boolean;
  onClose: () => void;
  onOpenPreferences: () => void;
}

const CookiePolicyDialog = ({
  open,
  onClose,
  onOpenPreferences,
}: CookiePolicyDialogProps) => {
  const theme = useTheme();

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          borderRadius: "16px",
          bgcolor: "#F9FAFB",
          m: 2,
          maxWidth: "800px !important",
        },
      }}
    >
      <DialogTitle sx={{ p: { xs: 3, sm: 4 }, pb: 0 }}>
        <Box
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          borderBottom="2px solid #000000"
        >
          <Typography variant="h6" fontWeight={600}>
            {COOKIE_POLICY.title}
          </Typography>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="right"
            width="100%"
            marginBottom="10px"
          >
            <Typography variant="body2" color="text.secondary">
              Effective Date: {COOKIE_POLICY.effectiveDate}
            </Typography>
            {/* <IconButton
              onClick={onClose}
              size="small"
              sx={{
                color: "text.secondary",
                "&:hover": { color: "text.primary" },
              }}
            >
              <CloseIcon />
            </IconButton> */}
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: { xs: 3, sm: 4 }, pt: { xs: 2, sm: 3 } }}>
        {/* Effective Date */}
        <Typography variant="body2" mb={2} color="text.secondary">
          *Effective Date : {COOKIE_POLICY.effectiveDate}
        </Typography>

        {/* Intro paragraphs */}
        {COOKIE_POLICY.introText.map((text, index) => (
          <Typography key={index} variant="body2" mb={2}>
            {text}
          </Typography>
        ))}

        {/* What are cookies */}
        <Typography variant="subtitle1" fontWeight={600} mt={3} mb={1}>
          {COOKIE_POLICY.whatAreCookies.title}
        </Typography>
        <Typography variant="body2" mb={2}>
          {COOKIE_POLICY.whatAreCookies.description}
        </Typography>

        <Typography variant="body2" fontWeight={600} mb={1}>
          The term "cookies" includes cookies, tags and pixels. Our websites may
          use cookies to:
        </Typography>

        {/* Bullet list */}
        <List dense sx={{ pl: 2 }}>
          {COOKIE_POLICY.whatAreCookies.purposes.map((text, index) => (
            <ListItem
              key={index}
              disableGutters
              sx={{ alignItems: "flex-start" }}
            >
              <ListItemText
                primaryTypographyProps={{ variant: "body2" }}
                primary={`• ${text}`}
              />
            </ListItem>
          ))}
        </List>

        {/* Usage note */}
        <Typography variant="body2" mt={2}>
          {COOKIE_POLICY.whatAreCookies.usageNote}
        </Typography>

        {/* Link note */}
        <Typography variant="body2" mt={2} mb="47px">
          {COOKIE_POLICY.whatAreCookies.preferencesNote}{" "}
          <Button
            onClick={onOpenPreferences}
            sx={{
              fontFamily: "Poppins",
              fontWeight: 700,
              // fontSize: "20px",
              lineHeight: "30px",
              letterSpacing: 0,
              textAlign: "justify",
              verticalAlign: "middle",
              textDecoration: "underline",
              textDecorationStyle: "solid",
              textDecorationOffset: 0,
              textDecorationThickness: 0,
              textDecorationSkipInk: "auto",
              color: "#222222",
              textTransform: "none",

            }}
          >
            {COOKIE_POLICY.buttons.preferences}
          </Button>
        </Typography>

        {/* Understood Button */}
        <Box display="flex" justifyContent="flex-end" mt={4}>
          <Button
            variant="contained"
            color="primary"
            size="medium"
            onClick={onClose}
            sx={{
              borderRadius: "9999px",
              textTransform: "none",
              fontWeight: 600,
              px: { xs: 3, sm: 4 },
              py: { xs: 1, sm: 1.5 },
              backgroundColor: "#4988C8",
              "&:hover": {
                backgroundColor: "#3b6da0",
              },
            }}
          >
            {COOKIE_POLICY.buttons.understood}
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default CookiePolicyDialog;
