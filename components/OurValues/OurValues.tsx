import React from "react";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import {
  our_values_icon1,
  our_values_icon2,
  our_values_icon3,
  our_values_icon4,
  our_values_icon5,
} from "@/public/index";
import "./our-values.scss";

const values = [
  { label: "Work Smart", icon: our_values_icon1 },
  { label: "Ownership", icon: our_values_icon2 },
  { label: "Integrity", icon: our_values_icon3 },
  { label: "Create value", icon: our_values_icon4 },
  { label: "Be Adroit", icon: our_values_icon5 },
  { label: " " },
];

const OurValues = () => {
  return (
    <Box className="our-values-section">
      <Box className="our-values-container">
        <Typography variant="h4" className="section-title">
          Our Values
        </Typography>
        <Box className="values-content">
          <Box className="values-text">
            <Typography className="values-description">
              Our values are the foundation of everything we do. They guide our
              decisions, <span>define</span> our <span>culture,</span> and{" "}
              <span>shape</span> the way we work with clients and each other.
            </Typography>
          </Box>
          <Box
            sx={{
              width: "1px",
              minWidth: "1px",
              height: "600px",
              flexShrink: 0,
              alignSelf: "center",
              borderRadius: "2px",
              marginX: 4,
              background:
                "linear-gradient(180deg, #030303 -50.98%, #1E4154 4.08%, #418FBA 48.49%, #030303 129.31%)",
            }}
          />

          <Box className="values-flex">
            <Box className="row">
              <Box className="value-card">
                <Box className="icon-wrapper">
                  <Image
                    src={our_values_icon1}
                    alt="Work Smart"
                    width={62}
                    height={62}
                    className="icon"
                    style={{ objectFit: "contain" }}
                  />
                </Box>
                <Typography className="value-text-title">Work Smart</Typography>
              </Box>
              <Box
                sx={{
                  width: "1px",
                  minWidth: "1px",
                  height: "159px",
                  flexShrink: 0,
                  alignSelf: "center",
                  borderRadius: "2px",
                  marginX: 4,
                  background:
                    "linear-gradient(180deg, #030303 -50.98%, #1E4154 4.08%, #418FBA 48.49%, #030303 129.31%)",
                }}
              />
              <Box className="value-card">
                <Box className="icon-wrapper">
                  <Image
                    src={our_values_icon2}
                    alt="Ownership"
                    width={62}
                    height={62}
                    className="icon"
                    style={{ objectFit: "contain" }}
                  />
                </Box>
                <Typography className="value-text-title">Ownership</Typography>
              </Box>
            </Box>

            <Box
              sx={{
                height: "1px",
                width: "100%",
                alignSelf: "center",
                borderRadius: "2px",
                marginY: 2,
                background:
                  "linear-gradient(90deg, #030303 -50.98%, #1E4154 4.08%, #418FBA 48.49%, #030303 129.31%)",
              }}
            />

            <Box className="row">
              <Box className="value-card">
                <Box className="icon-wrapper">
                  <Image
                    src={our_values_icon3}
                    alt="Work Smart"
                    width={62}
                    height={62}
                    className="icon"
                    style={{ objectFit: "contain" }}
                  />
                </Box>
                <Typography className="value-text-title">Integrity</Typography>
              </Box>
              <Box
                sx={{
                  width: "1px",
                  minWidth: "1px",
                  height: "159px",
                  flexShrink: 0,
                  alignSelf: "center",
                  borderRadius: "2px",
                  marginX: 4,
                  background:
                    "linear-gradient(180deg, #030303 -50.98%, #1E4154 4.08%, #418FBA 48.49%, #030303 129.31%)",
                }}
              />
              <Box className="value-card">
                <Box className="icon-wrapper">
                  <Image
                    src={our_values_icon4}
                    alt="Ownership"
                    width={62}
                    height={62}
                    className="icon"
                    style={{ objectFit: "contain" }}
                  />
                </Box>
                <Typography className="value-text-title">Create value</Typography>
              </Box>
            </Box>

            <Box
              sx={{
                height: "1px",
                width: "100%",
                alignSelf: "center",
                borderRadius: "2px",
                marginY: 2,
                background:
                  "linear-gradient(90deg, #030303 -50.98%, #1E4154 4.08%, #418FBA 48.49%, #030303 129.31%)",
              }}
            />

            <Box className="row">
              <Box className="value-card">
                <Box className="icon-wrapper">
                  <Image
                    src={our_values_icon5}
                    alt="Work Smart"
                    width={62}
                    height={62}
                    className="icon"
                    style={{ objectFit: "contain" }}
                  />
                </Box>
                <Typography className="value-text-title">Be Adroit</Typography>
              </Box>
              <Box
                sx={{
                  width: "1px",
                  minWidth: "1px",
                  height: "159px",
                  flexShrink: 0,
                  alignSelf: "center",
                  borderRadius: "2px",
                  marginX: 4,
                  background:
                    "linear-gradient(180deg, #030303 -50.98%, #1E4154 4.08%, #418FBA 48.49%, #030303 129.31%)",
                }}
              />
              <Box className="value-card">
                {/* <Box className="icon-wrapper">
                  <Image
                    src={our_values_icon2}
                    alt="Ownership"
                    width={56}
                    height={56}
                    className="icon"
                    style={{ objectFit: "contain" }}
                  />
                </Box>
                <Typography className="value-text">Ownership</Typography> */}
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default OurValues;
