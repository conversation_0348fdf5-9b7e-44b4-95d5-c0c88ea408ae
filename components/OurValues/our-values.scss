.our-values-section {
  background-color: #022434;
  padding: 60px 0;
  color: #ffffff;
  padding-right: 82px;
  padding-left: 82px;

  .our-values-container {
    padding: 0 20px;

    .section-title {
      font-family: Inter !important;
      font-weight: 700;
      font-size: 40px;
      margin-bottom: 16px;
      letter-spacing: 0%;
      padding-bottom: 20px;
      border-bottom: 0.5px solid #418fba;
    }

    .values-content {
      display: flex;
      flex-direction: row;
      // flex-wrap: wrap;

      .values-text {
        min-width: 280px;
        // margin-right: 40px;
      }

      .values-flex {
        width: 100%;

        .row {
          width: 100%;
          display: flex;

          .value-card {
            display: flex;
            align-items: center;
            width: 100%;
            gap: 31px;
            justify-content: center;
            height: 181px;
            .value-text-title {
              font-family: Inter !important;
              font-weight: 400;
              font-style: Regular;
              font-size: 24px;
              letter-spacing: 0%;
              text-align: center;
              color: rgba(216, 213, 213, 1);
            }
          }
        }
      }

      // .values-flex {
      //     display: flex;
      //     flex-wrap: wrap;
      //     width: 100%;
      //     justify-content: center;

      //     .value-card {
      //         background-color: transparent;
      //         padding: 62px 0 66px 0;
      //         display: flex;
      //         align-items: center;
      //         justify-content: center;
      //         gap: 15px;
      //         flex: 1 0 calc(50% - 20px); // 2 per row, minus half the gap
      //         box-sizing: border-box;

      //         font-family: Inter !important;
      //         font-weight: 700;
      //         font-size: 24px;
      //         leading-trim: Cap height;
      //         line-height: 100%;
      //         letter-spacing: 0%;
      //         text-align: center;

      //         border: 0.62px solid;

      //         border-image-source: linear-gradient(270deg, #030303 -50.98%, #1E4154 4.08%, #418FBA 48.49%, #030303 129.31%);
      //         border-image-slice: 1;
      //         border-image-width: 1px;
      //         border-image-outset: 0;
      //         border-image-repeat: stretch;
      //         motion-rotation: 90deg;
      //         border-right: none !important;

      //         border-bottom: 1px solid #418FBA;
      //         border-left: 1px solid #418FBA;
      //     }
      // }
    }

    .values-description {
      margin-top: 60px;
      font-family: Montserrat !important;
      font-weight: 400;
      font-style: Regular;
      font-size: 16px;
      letter-spacing: 0%;
      vertical-align: middle;
      max-width: 550px;
      width: 100%;

      span {
        font-weight: 700;
        font-style: Bold;
      }
    }
  }
}

.values-grid {
  flex: 2;
}

@media (max-width: 800px) {
  .value-card {
    flex: 0 0 100%;
    padding: 32px 0 36px 0;
  }

  .values-flex {
    gap: 20px;
  }
}

.value-text {
  font-weight: 600;
  font-size: 16px;
}
