.How-we-work-container {
  // gap: 1px;
  background-image: url('/assets/images/png/grid_bg.png');
  background-size: 100% 100%; // Stretch to fit width and height
  background-position: center;
  background-repeat: no-repeat;
  // min-height: 600px;
  padding-bottom: 84px;
  display: flex;
  flex-direction: row;
  background-color: #01131f;
  background-color: #F6F6F6;
  padding-top: 126px;
  padding-right: 83px;
  padding-left: 83px;
  gap: 2rem;

  .leftSection {
    flex: 1 1 8%;
    padding-right: 2rem;

    .heading {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 1rem;
      font-family: Inter;
      font-weight: 700;
      font-size: 40px;

      letter-spacing: 0%;
      color: #021F2E;
      margin-bottom: 41px;

    }

    .description {
      font-family: Montserrat !important;
      font-weight: 400;
      font-size: 16px;
      leading-trim: Cap height;

      letter-spacing: 0%;
      vertical-align: middle;
      color: #021F2E;
      max-width: 400px;


      strong {
        font-family: Montserrat !important;
        font-weight: 700;
        font-size: 16px;
        leading-trim: Cap height;

        letter-spacing: 0%;
        vertical-align: middle;

      }
    }
  }

  .rightSection {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .stepNumber {
      font-family: Inter !important;
      font-weight: 700;
      font-size: 40px;

      letter-spacing: 0%;
      text-align: center;
      background: linear-gradient(90deg, #6BD0B9 10.58%, #404A78 62.02%);
      opacity: 21%;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1), -webkit-text-fill-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), background 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .stepContainer {
      display: flex;
      align-items: center;
      gap: 40px;

      &:hover {
        .stepCard {
          transform: scale(1.05);
          box-shadow: 0 4px 24px 0 rgba(215, 125, 70, 0.15);
        }

        .stepNumber {
          color: #D77D46;
          opacity: 1;
          background: none;
          -webkit-text-fill-color: #D77D46;
        }
      }
    }

    .stepCard {
      background: #021F2E;
      border-radius: 8px;
      color: #ffffff;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 20px 93px;
      width: 100%;
      border-radius: 8px;
      border: 0.5px solid;
      // border-image-source: linear-gradient(270deg, #030303 -50.98%, #1E4154 4.08%, #418FBA 48.49%, #030303 129.31%);
      transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);



      .cardContent {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0px;
      }



      .avatar {
        width: 72px;
        height: 72px;
        background: transparent;
        border-width: 0px, 0px, 0px, 0px;

        border-style: solid;

        border-color: #D77D46;
        color: #D77D46;


      }

      .stepTitle {
        font-family: Inter !important;
        font-weight: 400;
        font-size: 24px;
        leading-trim: Cap height;

        letter-spacing: 0%;
        text-align: center;

      }
    }
  }
}











.activeCard {
  background-color: #05202d;

  .stepTitle {
    font-weight: 700;
  }
}