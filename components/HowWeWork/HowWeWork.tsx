"use client";
import React from "react";
import "./HowWeWork.scss";
import { Box, Typography, Card, CardContent, Avatar } from "@mui/material";
import {
  HowWeWork_Card_iconBlue,
  HowWeWork_Card_icon1,
  HowWeWork_Card_icon2,
  HowWeWork_Card_icon3,
  HowWeWork_Card_icon4,
  HowWeWork_Card_icon5,
} from "@/public/index";
import Image from "next/image";
import { usePathname } from "next/navigation";

const steps = [
  { id: "01", title: "Idea Creation", active: true, icon: HowWeWork_Card_icon1 },
  { id: "02", title: "PoC Validation", icon: HowWeWork_Card_icon2 },
  { id: "03", title: "Prototyping", icon: HowWeWork_Card_icon3 },
  { id: "04", title: "System Designing", icon: HowWeWork_Card_icon4 },
  { id: "05", title: "MFG Release", icon: HowWeWork_Card_icon5 },
];

const HowWeWork = () => {
  const pathname = usePathname();
  // const icon =
  //   pathname === "/NewPage/" ? HowWeWork_Card_iconBlue : HowWeWork_Card_icon1;

  return (
    <Box className="How-we-work-container">
      <Box className="leftSection">
        <Typography variant="h4" className="heading">
          How we Work
        </Typography>
        <Typography className="description">
          We believe that{" "}
          <strong>
            great digital products are the result of thoughtful strategy
          </strong>
          , human-centered design, and close collaboration with our clients. Our
          process is designed to deliver innovative, scalable, and user-focused
          solutions while keeping communication transparent and expectations
          aligned every step of the way.
        </Typography>
      </Box>
      <Box className="rightSection">
        {steps.map((step, index) => (
          <Box className="stepContainer" key={index}>
            <Box key={`step-number-${step.id}`} className="stepNumber">
              {step.id}
            </Box>
            <Card
              key={`step-card-${step.id}`}
              className={`stepCard ${step.active ? "activeCard" : ""}`}
              elevation={0}
            >
              <CardContent className="cardContent">
                <Avatar className="avatar">
                  <Image
                    src={step.icon}
                    alt="Step Icon"
                    className="stepIcon"
                  />
                </Avatar>
                <Typography className="stepTitle">{step.title}</Typography>
              </CardContent>
            </Card>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default HowWeWork;
