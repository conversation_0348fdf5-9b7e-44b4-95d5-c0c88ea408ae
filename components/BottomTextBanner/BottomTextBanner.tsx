import React from "react";
import { Box, Typography } from "@mui/material";
import "./BottomTextBanner.scss";

interface BottomTextBannerProps {
  text: string;
  highlight: string;
}

const BottomTextBanner: React.FC<BottomTextBannerProps> = ({
  text,
  highlight,
}) => {
  // Split the text at the highlight
  const [before, after] = text.split(highlight);
  return (
    <Box className="bottom-text-container">
      <Typography className="bottom-text">
        {before}
        <span className="highlight">{highlight}</span>
        {after}
      </Typography>
    </Box>
  );
};

export default BottomTextBanner;
