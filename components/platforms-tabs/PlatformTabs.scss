.platform-tabs {
  background-color: #ffffff;
  // padding: 2rem;
  padding-right: 106px;
  padding-bottom: 116px;
  padding-left: 94px;

  .Mui-selected {
    color: #021f2e !important;
    font-family: Montserrat !important;
    font-weight: 700 !important;
    font-size: 24px;
    // line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
  }

  .title {
    font-family: Inter !important;
    padding-bottom: 80px;
    font-weight: 700;
    font-style: Bold;
    font-size: 40px;
    line-height: 100%;
    text-align: center;
    letter-spacing: 0%;
  }

  .tabs-section {
    display: flex;
    // gap: 2rem;
    justify-content: space-between;
    align-items: flex-start;
  }

  .tabs {
    min-width: 250px;
    // border-right: 2px solid #e0e0e0;
    width: 45%;

    .MuiTabs-indicator {
      display: none;
    }
  }

  .tab {
    font-family: Montserrat !important;
    font-weight: 400;
    font-size: 24px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    min-width: max-content;
    text-transform: none;
    justify-content: flex-start;
  }

  .tab-content {
    margin-top: 26px;
    display: flex;
    flex-direction: row;
    gap: 2rem;
    // flex: 1;

    .tab-text {
      max-width: 500px;

      .tab-number {
        font-size: 3rem;
        font-weight: bold;
        color: #d5723e;
        margin-bottom: 1rem;
      }

      .tab-heading {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
      }

      .tab-description {
        font-size: 1rem;
        color: #555;
      }
    }

    .tab-image {
      min-width: 300px;

      img,
      .MuiImage-root,
      .MuiBox-root img {
        // max-width: 320px;
        width: 100%;
        height: auto;
        display: block;
      }
    }
  }

  .bottom-section {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 67px;

    .bottom-text {
      font-family: Montserrat !important;
      font-weight: 500;
      font-size: 16px;
      letter-spacing: 0%;
      text-align: center;
      vertical-align: middle;
      max-width: 910px;

      span {
        font-family: Montserrat;
        font-weight: 700;
        font-size: 16px;
        letter-spacing: 0%;
        text-align: center;
        vertical-align: middle;
      }
    }
  }

  @media (max-width: 1400px) {
    .tab-content {
      gap: 1rem;

      .tab-image {
        min-width: 200px;

        // img,
        // .MuiImage-root,
        // .MuiBox-root img {
        //   // max-width: 200px;
        // }
      }
    }
  }

  @media (max-width: 900px) {
    .tab-content {
      flex-direction: column;
      align-items: center;
      gap: 1.5rem;

      .tab-image {
        min-width: 150px;

        img,
        .MuiImage-root,
        .MuiBox-root img {
          max-width: 150px;
        }
      }
    }
  }

  .fade-image {
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
  }

  .fade-image.fade-in {
    opacity: 1;
  }

  .fade-image.fade-out {
    opacity: 0;
  }
}
