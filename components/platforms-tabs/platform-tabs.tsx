"use client";

import React, { useState } from "react";
import { Tabs, Tab, Box, Typography } from "@mui/material";
import Image from "next/image";
import "./PlatformTabs.scss";
import {
  platform_components_img1,
  platform_components_img2,
  platform_components_img3,
  platform_components_img4,
  platform_components_img5,
  platform_components_img6,
} from "@/public/index";
import { Circle } from "@mui/icons-material";

const tabData = [
  {
    label: "Employee Management",
    image: platform_components_img1,
    heading: "Employee Management",
  },
  {
    label: "Operation Management",
    image: platform_components_img2,
    heading: "Operation Management",
  },
  {
    label: "Marketing Campaigning",
    image: platform_components_img3,
    heading: "Marketing Campaigning",
  },
  {
    label: "Project Management",
    image: platform_components_img4,
    heading: "Project Management",
  },
  {
    label: "Finance & Payroll",
    image: platform_components_img5,
    heading: "Finance & Payroll",
  },
  {
    label: "IT Ticketing System",
    image: platform_components_img6,
    heading: "IT Ticketing System",
  },
];

const PlatformTabs = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [fade, setFade] = useState(true);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setFade(false);
    setTimeout(() => {
      setSelectedTab(newValue);
      setFade(true);
    }, 200); // duration matches CSS
  };

  const { heading, image } = tabData[selectedTab];

  return (
    <Box className="platform-tabs">
      <Typography variant="h4" className="title">
        Platform Components
      </Typography>
      <Box className="tabs-section">
        <Tabs
          orientation="vertical"
          value={selectedTab}
          onChange={handleChange}
          className="tabs"
        >
          {tabData.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              className="tab"
              icon={
                <Circle
                  sx={{ color: selectedTab === index ? "#D77D46" : "#D8D5D5" }}
                />
              }
              iconPosition="start"
            />
          ))}
        </Tabs>
        <Box className="tab-content">
          <Box
            className={`tab-image fade-image${fade ? " fade-in" : " fade-out"}`}
          >
            <Image src={image} alt={heading} />
          </Box>
        </Box>
      </Box>

      <Box className="bottom-section">
        <Typography className="bottom-text">
          SyncMaster enables real-time task assignment, creates a seamless
          ecosystem for{" "}
          <span>
            project management, employee management, finance, payroll, and CRM
            activities
          </span>{" "}
          including{" "}
          <span>mass email campaigns and client response tracking.</span> The
          platform also features a user friendly interface that allows clients
          to view real-time project progress and provide feedback or
          suggestions. This interactive capability enhances client satisfaction,
          improves delivery efficiency, and supports better decision making for
          both internal teams and clients.
        </Typography>
      </Box>
    </Box>
  );
};

export default PlatformTabs;
