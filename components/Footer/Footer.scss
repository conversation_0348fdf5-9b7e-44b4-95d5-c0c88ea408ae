@use "../../styles/variables" as *;

.footer {
  background-color: $footer-bg;
  color: $text-white;
  padding: 100px 0 1.5rem;

  .footerContainer {
    padding-left: 89px !important;
    padding-right: 82px !important;
    margin: 0px;
    width: 100%;
  }

  .topSection {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: space-between;
  }

  .logo {
    font-size: 1.8rem;
    font-weight: bold;
    font-family: serif;
  }

  .logoHighlight {
    color: #ff8c4a;
    font-size: 2rem;
  }

  .subtext {
    font-style: italic;
    font-size: 0.75rem;
    margin-top: -0.5rem;
  }

  .companyName {
    font-size: 0.9rem;
    margin-top: 0.5rem;
  }

  .columnTitle {
    margin-bottom: 0.5rem;
    font-family: Montserrat !important;
    font-weight: 700;
    font-style: Bold;
    font-size: 24px;
    letter-spacing: 0%;
    vertical-align: middle;
  }

  .list {
    list-style-type: disc;
    padding-left: 1rem;
    color: $text-white;

    li {
      font-family: Montserrat !important;
      font-weight: 400;
      font-style: Regular;
      font-size: 18px;
      line-height: 35px;
      letter-spacing: 0%;
      vertical-align: middle;
    }
  }

  .socialIcons {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;

    button {
      background-color: transparent;
      color: #fff;
      border-radius: 50%;
      padding: 0.5rem;
      transition: background-color 0.3s;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .bottomSection {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 2rem;
    padding-top: 1rem;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    font-size: 0.8rem;

    .policyLinks {
      display: flex;
      gap: 2rem;

      > * {
        cursor: pointer;
        transition: color 0.3s;

        &:hover {
          color: #ff8c4a;
        }
      }
    }
  }
}
