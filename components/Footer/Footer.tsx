// components/Footer/Footer.tsx
import React from "react";
import "./Footer.scss";
import { Box, Container, Typography, IconButton } from "@mui/material";
import {
  footer_company_logo,
  footer_insta,
  InstagramIcon,
  LinkedInIcon,
  XIcon,
  YouTubeIcon,
} from "@/public/index";
import Image from "next/image";
import Link from "next/link";
import packageJson from "@/package.json";

const Footer: React.FC = () => {
  return (
    <Box className="footer">
      <Container
        className="footerContainer"
        maxWidth={false}
        disableGutters
        sx={{ width: "100%" }}
      >
        <Box
          className="topSection"
          sx={{ display: "flex", flexWrap: "wrap", gap: 4 }}
        >
          <Box
            sx={{
              flex: { xs: "0 0 100%", md: "0 0 25%" },
              mb: { xs: 2, md: 0 },
            }}
          >
            <Image
              src={footer_company_logo}
              alt="Company Logo"
              className="footerLogo"
              width={150}
              height={150}
            />
            <Typography className="companyName">
              Aadvik Teklabs Private Limited
            </Typography>
          </Box>

          <Box
            className="socialIcons"
            sx={{
              flex: { xs: "0 0 100%", md: "0 0 8.33%" },
              display: "flex",
              alignItems: "center",
              justifyContent: { xs: "flex-start", md: "center" },
            }}
          >
            <IconButton>
              <Image src={InstagramIcon} alt="Instagram" />
            </IconButton>
            <IconButton>
              <Image src={XIcon} alt="X" />
            </IconButton>
            <IconButton>
              <Image src={LinkedInIcon} alt="LinkedIn" />
            </IconButton>
            <IconButton>
              <Image src={YouTubeIcon} alt="YouTube" />
            </IconButton>
          </Box>
        </Box>

        <Box
          sx={{
            display: "flex",
            width: "100%",
            justifyContent: "space-between",
            marginTop: "88px",
          }}
        >
          <Box
            sx={{
              flex: { xs: "0 0 50%", md: "0 0 16.66%" },
              mb: { xs: 2, md: 0 },
            }}
          >
            <Typography className="columnTitle">Services</Typography>
            <ul className="list">
              <li>
                <Link href="/services/ai-visiontek">AI and Vision-Tek</Link>
              </li>
              <li>
                <Link href="/services/web-technologies">Web Technologies</Link>
              </li>
              <li>
                <Link href="/services/embedded-iot">Embedded & IoT</Link>
              </li>
              <li>
                <Link href="/services/mechanical-design">Mechanical Design</Link>
              </li>
              <li>
                <Link href="/system-testing-reliability">Certification Testing</Link>
              </li>
              <li>
                <Link href="#">Branding & Creativity</Link>
              </li>
              <li>
                <Link href="#">Technology Consulting</Link>
              </li>
            </ul>
          </Box>

          <Box
            sx={{
              flex: { xs: "0 0 50%", md: "0 0 16.66%" },
              mb: { xs: 2, md: 0 },
            }}
          >
            <Typography className="columnTitle">Industries</Typography>
            <ul className="list">
              <li>
                <Link href="/industries/smart-farming">Smart Farming</Link>
              </li>
              <li>
                <Link href="#">Smart Factories</Link>
              </li>
              <li>
                <Link href="#">Smart Metering</Link>
              </li>
              <li>
                <Link href="#">Security Systems</Link>
              </li>
              <li>
                <Link href="/industries/heating-ventilation">Heating & Ventilation</Link>
              </li>
              <li>
                <Link href="/industries/smart-home">Smart Home & Building</Link>
              </li>
              <li>
                <Link href="#">Locomotive tracking</Link>
              </li>
            </ul>
          </Box>

          <Box
            sx={{
              flex: { xs: "0 0 50%", md: "0 0 16.66%" },
              mb: { xs: 2, md: 0 },
            }}
          >
            <Typography className="columnTitle">Resources</Typography>
            <ul className="list">
              <li>
                <Link href="#">Blogs & Write-ups</Link>
              </li>
              <li>
                <Link href="#">PoC & Evaluation</Link>
              </li>
              <li>
                <Link href="#">Technical Notes</Link>
              </li>

              <li>
                <Link
                  href="http://app-adv-dev-001.s3-website.ap-south-1.amazonaws.com/login/"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Platform Demo
                </Link>
              </li>
              <li>
                <Link
                  href="https://aadvik-crm-dc972.web.app/"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Sync Master
                </Link>
              </li>
            </ul>
          </Box>

          <Box
            sx={{
              flex: { xs: "0 0 50%", md: "0 0 16.66%" },
              mb: { xs: 2, md: 0 },
            }}
          >
            <Typography className="columnTitle">About</Typography>
            <ul className="list">
              <li>
                <Link href="#">Company</Link>
              </li>
              <li>
                <Link href="#">Careers</Link>
              </li>
              <li>
                <Link href="#">Terms</Link>
              </li>
              <li>
                <Link href="#">Contact</Link>
              </li>
            </ul>
          </Box>
        </Box>

        <Box className="bottomSection">
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
            <Typography>© 2025 All Rights Reserved.</Typography>
            <Typography variant="caption" sx={{ color: 'text.secondary', mt: 0.5 }}>
              Version {packageJson.version}
            </Typography>
          </Box>
          <Box className="policyLinks">
            <Link href="/privacy-policy">
              <Typography>Privacy Policy</Typography>
            </Link>
            <Link href="/terms-conditions">
              <Typography>Terms & Conditions</Typography>
            </Link>
            <Link href="/cookies-policy">
              <Typography>Cookie Policy</Typography>
            </Link>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
