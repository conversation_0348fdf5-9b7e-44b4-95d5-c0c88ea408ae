.iot-solutions-container {
  display: flex;
  min-height: 100vh;
}

.left-section {
  background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
  flex: 1;
  padding: 80px 60px;
  display: flex;
  flex-direction: column;
  //   justify-content: center;
  color: white;

  .main-title {
    font-size: 3.5rem;
    font-weight: bold;
    line-height: 1.2;
    margin-bottom: 40px;
    margin-top: 0;
  }

  .description {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 30px;
    opacity: 0.95;
  }
}

.right-section {

  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0;
  width: 100%;
  max-width: 800px;
}

.solution-card {
  border: 0.5px solid #D77D46;
  border-right: none;
  border-bottom: none;
  padding: 40px;
  position: relative;
  transition: all 0.3s ease;

  // Right column cards
  &:nth-child(even) {
    border-right: 1px solid #2d3748;
  }

  // Bottom row cards
  &:nth-child(5),
  &:nth-child(6) {
    border-bottom: 1px solid #2d3748;
  }

  &:hover {
    background: #243446;
    transform: translateY(-2px);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
  }

  .card-number {
    font-size: 4rem;
    font-weight: bold;
    color: #4a5568;
    line-height: 1;
  }

  .icon-container {
    color: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;

    .card-icon {
      width: 48px;
      height: 48px;
    }
  }

  .card-title {
    color: white;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 16px;
    margin-top: 0;
  }

  .card-description {
    color: #a0aec0;
    font-size: 0.95rem;
    line-height: 1.6;
    margin: 0;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .iot-solutions-container {
    flex-direction: column;
  }

  .left-section,
  .right-section {
    padding: 40px 30px;
  }

  .left-section {
    .main-title {
      font-size: 2.5rem;
    }
  }

  .cards-grid {
    grid-template-columns: 1fr;
    max-width: 500px;
  }

  .solution-card {
    padding: 30px;
    border-right: none !important;
    border-bottom: none;

    &:last-child {
      border-bottom: 1px solid #2d3748;
    }

    .card-number {
      font-size: 3rem;
    }
  }
}

// Additional responsive breakpoints
@media (max-width: 480px) {
  .left-section,
  .right-section {
    padding: 30px 20px;
  }

  .left-section {
    .main-title {
      font-size: 2rem;
    }

    .description {
      font-size: 1rem;
    }
  }

  .solution-card {
    padding: 25px;

    .card-number {
      font-size: 2.5rem;
    }

    .card-title {
      font-size: 1.2rem;
    }

    .card-description {
      font-size: 0.9rem;
    }
  }
}
