"use client";

import { Box, Typography, List, ListItem, ListItemText } from "@mui/material";
import React from "react";

interface SolutionCard {
  id: string;
  title: string;
  description: string | string[];
  // icon?: React.ReactNode;
}

const cards: SolutionCard[] = [
  {
    id: "01",
    title: "Machine Diagnostics",
    description:
      "Capture real‑time data from machines to monitor performance, detect anomalies, and minimize unexpected breakdowns.",
  },
  {
    id: "02",
    title: "Predictive Maintenance",
    description: [
      "Forecast wear & tear on critical components",
      "Optimize service cycles",
      "Reduce unplanned downtime",
    ],
  },
  {
    id: "03",
    title: "Real‑time Data Analysis",
    description:
      "Continuous data collection for instant insights and better decision‑making on the shop floor.",
  },
  {
    id: "04",
    title: "Asset Monitoring",
    description:
      "Track and monitor critical assets in real time to ensure optimal performance and prevent costly failures.",
  },
  {
    id: "05",
    title: "Energy Management",
    description:
      "Optimize energy consumption with smart monitoring and automated control systems.",
  },
  {
    id: "06",
    title: "Quality Control",
    description: [
      "Automated quality‑assurance processes",
      "Maintain consistent product standards",
      "Reduce waste",
    ],
  },
];

const IoTSolutions = () => (
  <Box sx={{ display: "flex", minHeight: "100vh" }}>
    {/* ───── Left orange panel ───── */}
    <Box
      sx={{
        flex: 1,
        p: { xs: 4, md: 8 },
        color: "#fff",
        background:
          "linear-gradient(135deg, #e67e22 0%, #d35400 100%)",
      }}
    >
      <Typography
        variant="h3"
        sx={{ fontWeight: 700, lineHeight: 1.2, mb: 5 }}
      >
        Our IIoT solutions are purposely built for
      </Typography>

      <Typography sx={{ mb: 3, fontSize: "1.1rem", lineHeight: 1.6 }}>
        Designing and delivering tailored IIoT solutions that bring intelligence
        to devices and industrial machines. Aadvik TekLabs partners with OEMs
        and industrial product companies to unlock the full potential of
        connected systems.
      </Typography>

      <Typography sx={{ fontSize: "1.1rem", lineHeight: 1.6 }}>
        Our consulting approach translates your operational needs into scalable
        IIoT architectures that drive tangible outcomes.
      </Typography>
    </Box>

    {/* ───── Right grid of cards ───── */}
    <Box
      sx={{
        flex: 1,
        display: "grid",
        gridTemplateColumns: "repeat(2, 1fr)",
        "& > .card": {
          // draw the grid lines once, no nth‑child fuss
          borderRight: "1px solid #D77D46",
          borderBottom: "1px solid #D77D46",
          p: { xs: 3, md: 5 },
          background: "#002635", // deep navy
          transition: "background 0.3s",
          "&:hover": { background: "#072c3f" },
        },
      }}
    >
      {cards.map(({ id, title, description /*, icon */ }) => (
        <Box key={id} className="card">
          {/* Number & optional icon row */}
          <Box sx={{ display: "flex", justifyContent: "space-between" }}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                color: "rgba(255,255,255,0.08)",
              }}
            >
              {id}
            </Typography>
            {/* <Box>{icon}</Box> */}
          </Box>

          {/* Title */}
          <Typography
            variant="h6"
            sx={{ color: "#fff", fontWeight: 600, mt: 2, mb: 1 }}
          >
            {title}
          </Typography>

          {/* Description: list or paragraph */}
          {Array.isArray(description) ? (
            <List dense sx={{ pl: 2 }}>
              {description.map((d, i) => (
                <ListItem
                  key={i}
                  disableGutters
                  sx={{
                    display: "list-item",
                    listStyleType: "disc",
                    color: "#b3c1cf",
                    py: 0.5,
                  }}
                >
                  <ListItemText primary={d} />
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography sx={{ color: "#b3c1cf" }}>{description}</Typography>
          )}
        </Box>
      ))}
    </Box>
  </Box>
);

export default IoTSolutions;
