"use client";

import React from "react";
import "./MechanicalDesign.scss";
import { Box, Container, Divider, Typography } from "@mui/material";

const data = [
  {
    number: "01",
    title: "Conceptual Design",
    description:
      "Concept design marks the beginning of bringing ideas to life. The creation of 3D models, attachments, BOMs, and mock-ups ensures the product’s unique market requirements.",
  },
  {
    number: "02",
    title: "Detailed Product Design",
    description:
      "Conceptual sketches are further enhanced into detailed and functional models using advanced modeling software. It includes FEA, GD&T, tolerancing, and further up to design validation.",
  },
  {
    number: "03",
    title: "Design Simulation",
    description:
      "We ensure that every design must withstand real-world performance, stress, and thermal conditions. We use linear & non-linear FEA and CFD tools to simulate a product's response to the design and real world.",
  },
  {
    number: "04",
    title: "Prototyping",
    description:
      "We can assist you in prototype development using 3D printing techniques like FDM, SLA, and SLS. Reverse Engineering is also supported. Rubber, aluminum casting, and precision machining for function and product validation.",
  },
  {
    number: "05",
    title: "Tool Development",
    description:
      "We provide design consultancy and development support for R&D fixtures, tool jigs, press tools, plastic injection tools, and metal mold tools. Our designs are optimized for cost, performance, and aesthetics.",
  },
  {
    number: "06",
    title: "Certification Testing",
    description:
      "With expertise in design and simulation, we ensure your product development aligns with safety certifications and industry standards. We prepare thorough documentation to speed up the certification process.",
  },
];

const MechanicalDesign = () => {
  return (
    <section className="mechanical-design">
      <Box className="header-container">
        <Typography variant="h4" className="title">
          Mechanical Design
        </Typography>
        <Box className="description-container">
          <Typography variant="body1" className="left-description">
            Our expert designers bring years of experience in mechanical design
            and development, spanning key industry sectors such as IoT devices,
            industrial automation, consumer electronics, and utility appliances.
            Team has consistently delivered top-tier mechanical solutions,
            meeting the most demanding client requirements start from IP-rated,
            rugged, and safety critical designs with cost-effective, high-volume
            production solutions.
          </Typography>
          <Typography className="right-description">
            With deep expertise in design and simulation, we ensure each is
            meticulously optimized for performance, durability, and cost across
            every product development phase.
          </Typography>
        </Box>
      </Box>

      <div className="grid-container">
        {data.map((item, index) => (
          <div key={index} className="card">
            <div className="number">{item.number}</div>
            <div className="title">{item.title}</div>
            <Divider
              className="divider"
              sx={{ borderColor: "#fff", borderWidth: 1, margin: "10px 0" }}
            />
            <div className="text">{item.description}</div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default MechanicalDesign;
