.mechanical-design {
  padding-bottom: 193px;
  padding-top: 124px;
  padding-left: 95px;
  padding-right: 95px;
  background-color: #fff;
  color: #001219;

  .header-container {
    display: flex;
    flex-direction: column;
    margin-bottom: 78px;
  }



  .title {
    font-family: Inter !important;
    font-weight: 800;
    font-size: 48px;
    line-height: 100%;
    letter-spacing: 0%;
    text-align: left;
    margin-bottom: 50px;
  }



  .description-container {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-description {
      font-family: Montserrat !important;
      font-weight: 500;
      font-size: 16px;
      line-height: 100%;
      letter-spacing: 0%;
      max-width: 500px;
      color: #021F2E;

    }

    .right-description {
      font-family: Montserrat !important;
      font-weight: 500;
      font-size: 16px;
      leading-trim: Cap height;
      line-height: 100%;
      letter-spacing: 0%;
      color: #021F2E;
      max-width: 453px;
    }
  }

  .grid-container {
    display: grid;
    grid-template-columns: repeat(3, minmax(360px, 1fr));
    gap: 18px;

    .card {
      background-color: #002233;
      padding: 31px;
      border-radius: 0.5rem;
      color: white;
      transition: background-color 0.3s ease, color 0.3s ease;

      &:hover {
        background-color: #ec8445;
        color: #FFFFFF;

        .text {
          color: #FFFFFF;
        }
      }

      .number {
        font-family: Montserrat !important;
        font-weight: 700;
        font-size: 96px;
        line-height: 100%;
        letter-spacing: 0%;
        vertical-align: middle;
        color: #FFFFFF;
        opacity: 15%;
      }

      .title {
        margin-top: 93px;
        font-family: Montserrat !important;
        font-weight: 700;
        font-size: 24px;
        line-height: 100%;
        letter-spacing: 0%;
        vertical-align: middle;
        // text-decoration: underline;
        // text-underline-offset: 15px;
        margin-bottom: 0px;
        min-width: fit-content;
      }

      .text {
        font-size: 0.95rem;
        color: #FFFFFF;
      }
    }
  }
}