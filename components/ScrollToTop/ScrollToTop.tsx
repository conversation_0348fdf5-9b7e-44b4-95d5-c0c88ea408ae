"use client";
import React, { useState, useEffect } from "react";
import { Fab } from "@mui/material";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";

const ScrollToTop = () => {
  const [isVisible, setIsVisible] = useState(false);

  // Show button when page is scrolled up to given distance
  const toggleVisibility = () => {
    if (window.pageYOffset > 300) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  };

  // Set the top cordinate to 0
  // make scrolling smooth
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  useEffect(() => {
    window.addEventListener("scroll", toggleVisibility);
    return () => {
      window.removeEventListener("scroll", toggleVisibility);
    };
  }, []);

  return (
    <>
      {isVisible && (
        <Fab
          onClick={scrollToTop}
          sx={{
            position: "fixed",
            bottom: "30px",
            right: "30px",
            backgroundColor: "rgba(15, 255, 183, 0.9)",
            backdropFilter: "blur(8px)",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            transition: "all 0.3s ease-in-out",
            width: "48px",
            height: "48px",
            zIndex: 1000,
            "&:hover": {
              backgroundColor: "rgba(12, 230, 159, 0.95)",
              transform: "translateY(-5px)",
              boxShadow: "0 12px 40px rgba(0, 0, 0, 0.15)",
            },
            "& .MuiSvgIcon-root": {
              fontSize: "28px",
              transition: "transform 0.3s ease",
            },
            "&:hover .MuiSvgIcon-root": {
              transform: "translateY(-2px)",
            },
          }}
          aria-label="scroll to top"
        >
          <KeyboardArrowUpIcon />
        </Fab>
      )}
    </>
  );
};

export default ScrollToTop;
