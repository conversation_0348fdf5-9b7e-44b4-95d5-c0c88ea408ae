.custom-iiot-section {
  display: flex;
  align-items: flex-start;
  background-color: #021f2b;
  color: #ffffff;
  padding: 100px 80px;
  gap: 64px;

  &.no-left-section {
    .iiot-right {
      flex: 1;
      max-width: 100%;
    }
  }

  .iiot-left {
    flex: 0.5;
    min-width: 395px;

    .iiot-heading {
      font-family: Montserrat !important;
      font-weight: 700;
      font-size: 32px;
      vertical-align: middle;
      margin-bottom: 20px;
    }

    .industries-iot-divider {
      border: 0.5px solid #ffffff !important;
      margin-bottom: 31px;
    }

    .iiot-subtext {
      font-family: Montserrat !important;
      font-weight: 500;
      font-size: 15px;
      font-style: normal;
      letter-spacing: 0%;
      vertical-align: middle;
      max-width: 331px;
      margin-bottom: 52px;
    }

    .iiot-list {
      padding: 0;

      .iiot-list-item {
        align-items: flex-start;
        padding: 6px 0;

        .MuiListItemIcon-root {
          min-width: 30px;

          img {
            width: 18px;
            height: 20px;
          }
        }

        .MuiListItemText-primary {
          font-family: Montserrat !important;
          font-weight: 500;
          font-size: 15px;
          font-style: normal;
          letter-spacing: 0%;
          vertical-align: middle;
        }
      }
    }
  }

  .iiot-right {
    flex: 0.5;
    max-width: 80%;

    .mechanical-testing-section {
      padding-bottom: 60px;

      .swiper-slide {
        display: flex;
        align-items: stretch;
        height: auto;
      }

      .card {
        background-color: #002233;
        padding: 17px 28px;
        border-radius: 8.98px;
        color: white;
        transition: background-color 0.3s ease, color 0.3s ease;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        width: 405px;
        height: 490px;
        min-width: 405px;
        min-height: 490px;
        max-width: 100%;
        box-sizing: border-box;
        border: 0.56px solid #d77d46 !important;
        opacity: 1;

        &:hover {
          background-color: var(--card-hover-bg, #d77d46);
          color: var(--card-hover-text, #ffffff);

          .title,
          .divider,
          .description,
          .text {
            color: var(--card-hover-text, #ffffff) !important;
            border-color: var(--card-hover-text, #ffffff) !important;
          }
        }

        .number {
          font-family: Montserrat !important;
          font-weight: 700;
          font-size: 96px;
          line-height: 100%;
          letter-spacing: 0%;
          vertical-align: middle;
          color: #ffffff;
          opacity: 15%;
        }

        .title {
          margin-top: 62px;
          font-family: Montserrat !important;
          font-weight: 700;
          font-size: 24px;
          line-height: 100%;
          letter-spacing: 0%;
          vertical-align: middle;
          margin-bottom: 35px;
          min-width: fit-content;
          color: #ffffff;
          text-align: left;
          width: 100%;
        }

        .text {
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: normal;
          letter-spacing: 0%;
          vertical-align: middle;

          .description {
            font-family: Montserrat !important;
            font-weight: 500;
            font-style: normal;
            font-size: 15px;
            letter-spacing: 0%;
            vertical-align: middle;
          }
        }
      }
    }
  }

  @media (max-width: 960px) {
    flex-direction: column;
    padding: 60px 24px;

    .iiot-left,
    .iiot-right {
      max-width: 100%;
    }

    .iiot-left {
      margin-bottom: 48px;
    }

    .iiot-right {
      .mechanical-testing-section {
        .swiper-slide {
          width: 100%;
        }

        .card {
          width: 100%;
          min-width: 0;
        }
      }
    }

    &.no-left-section {
      .iiot-right {
        max-width: 100%;
      }
    }
  }
}