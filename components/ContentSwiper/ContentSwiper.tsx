"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import type { Swiper as SwiperType } from "swiper";
import { Autoplay, FreeMode } from "swiper/modules";
import "swiper/css";
import {
  Box,
  Divider,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from "@mui/material";
import Image from "next/image";
import { ExpertiseIcon } from "@/public";
import "./ContentSwiper.scss";
import { useRef } from "react";

interface TestCard {
  number: string;
  title: string;
  description: string | string[];
}

interface ContentSwiperProps {
  cards: TestCard[];
  listItems?: string[];
  heading?: string;
  subtext?: string;
}

const ContentSwiper = ({
  cards,
  listItems,
  heading,
  subtext,
}: ContentSwiperProps) => {
  const swiperRef = useRef<SwiperType | null>(null);

  // Duplicate cards array to ensure enough slides for looping
  const loopedCards =
    cards.length < 4 ? [...cards, ...cards, ...cards, ...cards] : cards;

  const handleMouseEnter = () => {
    if (swiperRef.current && swiperRef.current.autoplay) {
      swiperRef.current.autoplay.pause(); // Pause for smooth stop on hover
    }
  };

  const handleMouseLeave = () => {
    if (swiperRef.current && swiperRef.current.autoplay) {
      swiperRef.current.autoplay.resume(); // Resume autoplay smoothly
    }
  };

  // Determine if left section should be rendered
  const shouldRenderLeftSection = listItems?.length || heading || subtext;

  return (
    <Box
      className={`custom-iiot-section ${
        !shouldRenderLeftSection ? "no-left-section" : ""
      }`}
    >
      {/* Left Content: List (conditionally rendered) */}
      {shouldRenderLeftSection && (
        <Box className="iiot-left">
          {heading && (
            <Typography
              className="iiot-heading"
              dangerouslySetInnerHTML={{ __html: heading }}
            />
          )}
          {heading && <Divider className="industries-iot-divider" />}
          {subtext && (
            <Typography className="iiot-subtext">{subtext}</Typography>
          )}
          {listItems && listItems.length > 0 && (
            <List className="iiot-list">
              {listItems.map((item, idx) => (
                <ListItem key={idx} className="iiot-list-item">
                  <ListItemIcon>
                    <Image
                      src={ExpertiseIcon}
                      alt="icon"
                      width={24}
                      height={24}
                    />
                  </ListItemIcon>
                  <ListItemText primary={item} />
                </ListItem>
              ))}
            </List>
          )}
        </Box>
      )}

      {/* Right Content: Swiper */}
      <Box className="iiot-right">
        <Box className="mechanical-testing-section">
          <Swiper
            modules={[Autoplay, FreeMode]}
            spaceBetween={30}
            slidesPerView="auto"
            loop={true}
            speed={3000}
            autoplay={{
              delay: 1,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            }}
            freeMode={{ enabled: true, momentum: false, momentumRatio: 1 }}
            allowTouchMove={false}
            breakpoints={{
              600: { slidesPerView: 1.2, spaceBetween: 30 },
              700: { slidesPerView: 1.2, spaceBetween: 30 },
              800: { slidesPerView: 1.2, spaceBetween: 30 },
              1024: { slidesPerView: 1.5, spaceBetween: 30 },
              1200: { slidesPerView: 2, spaceBetween: 30 },
              1300: { slidesPerView: 2, spaceBetween: 30 },
              1400: { slidesPerView: 2.2, spaceBetween:30 },
              1500: { slidesPerView: 2.5, spaceBetween: 30 },
              1600: { slidesPerView: 3.5, spaceBetween: 30 },
              1700: { slidesPerView: 2.5, spaceBetween: 30 },
              1800: { slidesPerView: 3, spaceBetween: 30 },
              1900: { slidesPerView: 3, spaceBetween: 30 },
            }}

            style={{ padding: "0 20px 40px 20px" }}
            onSwiper={(swiper) => (swiperRef.current = swiper)}
          >
            {loopedCards.map((item, index) => (
              <SwiperSlide key={index}>
                <div
                  className="card"
                  onMouseEnter={handleMouseEnter}
                  onMouseLeave={handleMouseLeave}
                >
                  <div className="number">{item.number}</div>
                  <div className="title">
                    {item.title}
                    <Divider
                      className="divider"
                      sx={{
                        borderColor: "#fff",
                        borderWidth: 1,
                        margin: "10px 0",
                      }}
                    />
                  </div>
                  <div className="text">
                    {Array.isArray(item.description) ? (
                      <List
                        sx={{
                          padding: 0,
                          listStyleType: "disc",
                          color: "#fff",
                          "& .MuiListItem-root": {
                            display: "list-item",
                            padding: 0,
                            marginBottom: "8px",
                          },
                          "& .MuiListItemText-primary": {
                            color: "#fff",
                            fontSize: "0.9rem",
                          },
                          "& .MuiListItem-root::marker": {
                            color: "#fff",
                            fontSize: "1.2rem",
                          },
                        }}
                      >
                        {item.description.map((desc, i) => (
                          <ListItem key={i}>
                            <ListItemText primary={desc} />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Typography sx={{ color: "#fff", fontSize: "0.9rem" }}>
                        {item.description}
                      </Typography>
                    )}
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </Box>
      </Box>
    </Box>
  );
};

export default ContentSwiper;
