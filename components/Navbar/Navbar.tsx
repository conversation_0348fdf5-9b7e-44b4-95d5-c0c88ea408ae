"use client";
import React from "react";
import "./Navbar.scss";
import { dropdownData, industriesDropdownData } from "@/constant/index";
import { NavbarLogo } from "@/public/index";
import {
  AppBar,
  Box,
  Toolbar,
  Typography,
  Button,
  useTheme,
} from "@mui/material";
import Image from "next/image";
import { useRouter, usePathname } from "next/navigation";
import ServiceNavDropdown from "@/components/servicesGrid/servicesGrid";
import IndustriesNavDropdown from "../servicesGrid/IndustriesNavDropdown";

const navItems = [
  "Company",
  "Services",
  "Platforms",
  "Industries",
  "Solutions",
  "Let's Talk",
];

const Navbar: React.FC = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [active, setActive] = React.useState("Company");
  const [hovered, setHovered] = React.useState<string | null>(null);
  const [openDropdown, setOpenDropdown] = React.useState<string | null>(null);
  const theme = useTheme();

  React.useEffect(() => {
    if (pathname === "/") {
      setActive("Company");
    } else if (pathname.startsWith("/services")) {
      setActive("Services");
    } else if (pathname.startsWith("/platform")) {
      setActive("Platforms");
    } else if (pathname.startsWith("/industries")) {
      setActive("Industries");
    } else if (pathname.startsWith("/solutions")) {
      setActive("Solutions");
    } else if (pathname.startsWith("/lets-talk")) {
      setActive("Let's Talk");
    }
  }, [pathname]);

  return (
    <AppBar
      className="appbar-container"
      position="absolute"
      sx={{
        background: "transparent",
        boxShadow: "none",
        padding: "0 2rem",
        top: "20px",
        left: 0,
        width: "100%",
        zIndex: 10,
      }}
    >
      <Toolbar
        className="toolbar"
        disableGutters
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        {/* Logo */}
        <Box
          className="nav-logo"
          sx={{ display: "flex", alignItems: "center", cursor: "pointer" }}
          onClick={() => {
            setActive("Company");
            router.push("/");
          }}
        >
          <Image
            src={NavbarLogo}
            alt="Aadvik Teklabs Logo"
            width={202}
            height={78}
          />
        </Box>

        {/* Nav Items */}
        <Box sx={{ display: "flex", gap: 3, position: "relative" }}>
          {navItems.map((item) => {
            const isActive = item === active;
            const isHovered = item === hovered;
            const showUnderline = hovered ? isHovered : isActive;
            return (
              <Button
                key={item}
                onClick={() => {
                  setActive(item);
                  if (item === "Company") {
                    router.push("/");
                  } else if (item === "Services") {
                    router.push("");
                  } else if (item === "Platforms") {
                    router.push("/platform");
                  } else if (item === "Industries") {
                    router.push("");
                  } else if (item === "Solutions") {
                    router.push("");
                  } else if (item === "Let's Talk") {
                    router.push("/lets-talk");
                  }
                }}
                onMouseEnter={() => {
                  setHovered(item);
                  if (item === "Services") setOpenDropdown("services");
                  else if (item === "Industries") setOpenDropdown("industries");
                  else setOpenDropdown(null);
                }}
                onMouseLeave={() => {
                  setHovered(null);
                  if (item === "Services" && openDropdown === "services")
                    setOpenDropdown(null);
                  if (item === "Industries" && openDropdown === "industries")
                    setOpenDropdown(null);
                }}
                sx={{
                  color: "#fff",
                  fontWeight: isActive ? "bold" : "normal",
                  fontSize: "0.95rem",
                  position: "relative",
                  textTransform: "none",
                  "&:after": {
                    content: '""',
                    display: "block",
                    width: showUnderline ? "100%" : "0",
                    height: "2px",
                    backgroundColor: "#ff8c4a",
                    position: "absolute",
                    bottom: -4,
                    left: 0,
                    transition: "width 0.3s",
                  },
                }}
              >
                {item}
              </Button>
            );
          })}
          {/* ServiceNavDropdown Dropdowns */}
          {openDropdown === "services" && (
            <Box
              onMouseEnter={() => setOpenDropdown("services")}
              onMouseLeave={() => setOpenDropdown(null)}
              sx={{
                position: "absolute",
                top: "100%",
                left: 0,
                background: "#fff",
                zIndex: 100,
                boxShadow: 3,
                display: "flex",
                justifyContent: "center",
                transform: "translateX(-45%)",
              }}
            >
              <ServiceNavDropdown
                data={dropdownData}
                onClose={() => setOpenDropdown(null)}
                onNavigate={() => setActive("Services")}
              />
            </Box>
          )}
          {openDropdown === "industries" && (
            <Box
              onMouseEnter={() => setOpenDropdown("industries")}
              onMouseLeave={() => setOpenDropdown(null)}
              sx={{
                position: "absolute",
                top: "100%",
                left: 0,
                background: "#fff",
                zIndex: 100,
                boxShadow: 3,
                display: "flex",
                justifyContent: "center",
                transform: "translateX(-45%)",
              }}
            >
              <IndustriesNavDropdown
                data={industriesDropdownData}
                onClose={() => setOpenDropdown(null)}
                onNavigate={() => setActive("Industries")}
              />
            </Box>
          )}
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
