"use client";
import { Box, Typography } from "@mui/material";
import InfoIcon from "@mui/icons-material/Info"; // You can customize this icon
import "./AboutUs.scss";
import { rocket_icon, rocket_iconBlue } from "@/public/index";
import Image from "next/image";
import { usePathname } from "next/navigation";

const AboutUs = () => {
  const pathname = usePathname();
  const normalizedPath = pathname.replace(/\/$/, "").toLowerCase();
  const icon = normalizedPath === "/newpage" ? rocket_iconBlue : rocket_icon;

  return (
    <section className="about-us-section">
      <Box className="about-us-content">
        <Typography variant="h6" className="about-label">
          About us
        </Typography>
        <Box className="about-us-block">
          {/* Left */}
          <Box className="text-block">
            <Typography variant="h3" className="about-heading">
              <span className="highlight-green">Aadvik TekLabs</span> is a team
              of visionary innovators redefining business through{" "}
              <strong>AR, VR, MR,</strong> and{" "}
              <strong>AIoT technologies</strong>.
            </Typography>
          </Box>

          {/* Right */}
          <Box className="info-block">
            <Box className="icon-circle">
              <Image src={icon} alt="Ellipse Icon" width={72} height={72} />
            </Box>
            <Typography className="info-text">
              Our mission is to harness AIoT and vision technologies to solve
              business challenges and deliver cutting-edge solutions.{" "}
            </Typography>
            <Typography className="info-text-2">
              Our experienced team specializes in{" "}
              <span>Embedded Systems, AR/VR/MR</span>, and serves industries
              like Energy, T&D, Lighting, and Industrial Machinery.
            </Typography>
          </Box>
        </Box>
      </Box>
    </section>
  );
};

export default AboutUs;
