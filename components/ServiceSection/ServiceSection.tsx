import React from "react";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import "./ServiceSection.scss";
import {
  Cmsicon,
  MicroIcon,
  EcommerceIcon,
  ApplicationIcon,
  SeiIcon,
  DevopsIcon,
} from "@/public";

const services = [
  {
    title: "Content Management System (CMS)",
    description:
      "Our team is proficient in developing and customizing CMS solutions, enabling you to manage your website content effortlessly and efficiently with minimal involvement of dev team.",
    icon: Cmsicon,
  },
  {
    title: "Micro-services Development",
    description:
      "A microservices-based software applications built on a modular architecture of small, independent services. Each microservice is designed around a specific business capability, allowing for seamless development, deployment, and scaling.",
    icon: MicroIcon,
  },
  {
    title: "E-commerce Development",
    description:
      "We specialize in creating robust e-commerce platforms that provide seamless shopping experiences, integrating secure payment gateways, and ensuring scalability to support your business growth.",
    icon: EcommerceIcon,
  },
  {
    title: "Application Modernization Services",
    description:
      "We specialize in modernizing legacy applications to boost their performance, strengthen security, and ensure seamless compatibility with today's technologies.",
    icon: ApplicationIcon,
  },
  {
    title: "Search Engine Optimization (SEO)",
    description:
      "Our SEO specialists craft tailored strategies to enhance your website’s search engine visibility, driving organic traffic and expanding your online presence.",
    icon: SeiIcon,
  },
  {
    title: "DevOps Service",
    description:
      "We provide cloud infrastructure support services for seamless performance and sustainable growth to meet your business requirements.",
    icon: DevopsIcon,
  },
];

const ExpandedServices = () => {
  return (
    <Box className="expanded-services">
      <Typography className="section-title">
        Our Expanded Services Includes
      </Typography>

      <Box className="service-rows">
        {Array.from({ length: services.length / 2 }).map((_, rowIdx) => (
          <Box className="service-row" key={rowIdx}>
            {[0, 1].map((colIdx) => {
              const index = rowIdx * 2 + colIdx;
              const service = services[index];
              return (
                <Box className="service-box" key={service.title}>
                  <Image src={service.icon} alt={`${service.title} icon`} />
                  <Box>
                    <Typography className="service-title">
                      {service.title}
                    </Typography>
                    <Typography className="service-desc">
                      {service.description}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default ExpandedServices;
