.expanded-services {
  padding: 80px 24px;
  max-width: 1700px;
  margin: 0 auto;

  .section-title {
    font-family: Inter !important;
    font-weight: 800;
    font-style: Extra Bold;
    font-size: 48px;
    letter-spacing: 0%;

    margin-bottom: 100px;
  }

  .service-rows {
    display: flex;
    flex-direction: column;
    gap: 40px; // Add margin between rows
  }

  .service-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    // Remove border lines - we'll use separators instead
    position: relative;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 20px; // Add gap between boxes on mobile
    }
  }

  .service-box {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 30px;
    min-height: 200px; // Set minimum height for consistent box sizes
    position: relative;

    // Add vertical separator line between boxes (desktop only)
    &:not(:last-child)::after {
      content: "";
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1px;
      height: 60%; // Adjust height of separator line
      background-color: #135c88;

      @media (max-width: 768px) {
        display: none; // Hide separator on mobile
      }
    }

    @media (max-width: 768px) {
      min-height: auto; // Remove fixed height on mobile
      padding: 20px;
    }

    img {
      object-fit: contain;
      flex-shrink: 0;
    }

    .service-title {
      font-family: Montserrat !important;
      font-weight: 700;
      font-style: Bold;
      font-size: 24px;
      letter-spacing: 0%;
      vertical-align: middle;

      margin-bottom: 10px;
    }

    .service-desc {
      font-family: Montserrat !important;
      font-weight: 500;
      font-style: Medium;
      font-size: 16px;
      letter-spacing: 0%;
      max-width: 490px;
      color: #021f2e;
      line-height: 1.5; // Improve readability
    }
  }
}
