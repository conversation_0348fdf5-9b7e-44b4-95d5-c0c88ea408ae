.expanded-services {
  padding: 80px 24px;
  max-width: 1700px;
  margin: 0 auto;

  .section-title {
    font-family: Inter !important;
    font-weight: 800;
    font-style: Extra Bold;
    font-size: 48px;
    letter-spacing: 0%;

    margin-bottom: 100px;
  }

  .service-rows {
    display: flex;
    flex-direction: column;
  }

  .service-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    border-top: 1px solid #135c88;
    border-bottom: 1px solid #135c88;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .service-box {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 30px;
    border-right: 1px solid #135c88;

    &:last-child {
      border-right: none;
    }

    @media (max-width: 768px) {
      border-right: none;
      border-bottom: 1px solid #135c88;

      &:last-child {
        border-bottom: none;
      }
    }

    img {
      object-fit: contain;
      flex-shrink: 0;
    }

    .service-title {
      font-family: Montserrat !important;
      font-weight: 700;
      font-style: Bold;
      font-size: 24px;
      letter-spacing: 0%;
      vertical-align: middle;

      margin-bottom: 10px;
    }

    .service-desc {
      font-family: Montserrat !important;
      font-weight: 500;
      font-style: Medium;
      font-size: 16px;
      letter-spacing: 0%;
      max-width: 490px;
      color: #021f2e;
    }
  }
}
