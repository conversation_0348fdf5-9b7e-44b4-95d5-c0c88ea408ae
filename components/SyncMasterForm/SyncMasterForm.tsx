import React from "react";
import { TextF<PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from "@mui/material";
import "./SyncMasterForm.scss";

import { ReactNode } from "react";

interface ConnectFormProps {
  heading: ReactNode;
  highlight?: string; // optional now
}

const SyncMasterForm: React.FC<ConnectFormProps> = ({ heading }) => {
  return (
    <Box className="sync-master-container">
      <Box className="sync-master-content">
        <Box className="left-content">
          <Typography className="subtext">Connect with us !!</Typography>
          <Typography variant="h2">{heading}</Typography>
        </Box>
        <Box className="right-form">
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Type your name here"
            className="form-input"
          />
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Email Id"
            className="form-input"
          />
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Your Company Name"
            className="form-input"
          />
          <Button variant="contained" className="submit-button">
            Submit
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default SyncMasterForm;
