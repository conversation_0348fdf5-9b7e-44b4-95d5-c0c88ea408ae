.sync-master-container {
  margin: 67px 75px 100px 82px;
//   background-color: #03202f;

  .sync-master-content {
    display: flex;
    justify-content: space-between;
    padding: 65px 69px 52px 0px;
    color: #021F2E;
    flex-wrap: wrap;
    border-top: 0.5px solid;
    border-image-source: linear-gradient(
      270deg,
      #030303 -50.98%,
      #1e4154 4.08%,
      #418fba 48.49%,
      #030303 129.31%
    );
    border-image-slice: 1;

    .left-content {
      max-width: 500px;

      .subtext {
        font-family: Inter;
        font-weight: 400;
        font-size: 16px;
        letter-spacing: 0%;
        vertical-align: middle;
        margin-bottom: 23px;
      }

      h2 {
        font-family: Inter !important;
        font-weight: 700;
        font-style: Bold;
        font-size: 40px !important;
        line-height: 120% !important;
        letter-spacing: 0%;

        .highlight {
          color: #d77d46;
        }
      }
    }

    .right-form {
      // margin-top: 50px;
      min-width: 516px;
      display: flex;
      flex-direction: column;
      gap: 24px;

      .form-input {
        // background-color: #03202f;
        border-radius: 6px;

        .MuiInputBase-formControl {
          border-radius: 6px;
        }

        input,
        textarea {
          color: #021f2e !important;
        }

        fieldset {
          border-color: #d77d46;
        }
      }

      .submit-button {
        width: 150px;
        background: #d77d46;
        color: #ffffff;
        align-self: flex-end;
        text-transform: none;
        padding: 15px 45px;
        font-family: Poppins;
        font-weight: 700;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;

        &:hover {
          background-color: #c6702f;
        }
      }
    }
  }
}
