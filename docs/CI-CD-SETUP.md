# GitHub Actions CI/CD Pipeline Setup

This document provides instructions for setting up and testing the GitHub Actions CI/CD pipeline for automatic deployment to Firebase Hosting.

## Overview

The pipeline automatically:
- Triggers on push or PR merge to the `develop` branch
- Updates semantic version based on commit messages
- Builds the Next.js application using `npm run build:dev`
- Deploys to Firebase Hosting (fixed project: `websitedemo-32483`)
- Commits the updated version back to the repository
- Displays version in the website footer

## Prerequisites

1. **Firebase Project**: Ensure you have access to the Firebase project `websitedemo-32483`
2. **Firebase CLI**: Install Firebase CLI locally for initial setup
3. **GitHub Repository**: Repository should have a `develop` branch

## Setup Instructions

### 1. Generate Firebase CI Token

Run the following command locally to generate a Firebase CI token:

```bash
firebase login:ci
```

This will open a browser window for authentication and return a token. Copy this token.

### 2. Configure GitHub Secrets

In your GitHub repository, go to **Settings > Secrets and variables > Actions** and add:

- **Secret Name**: `FIREBASE_TOKEN`
- **Secret Value**: The token from step 1

### 3. Verify Firebase Configuration

Ensure your `firebase.json` is correctly configured:

```json
{
  "hosting": {
    "public": "build_dir",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  }
}
```

## Version Management

### Automatic Version Bumping

The pipeline automatically determines the version bump type based on commit messages:

- **Major version** (x.0.0): Commit messages containing `BREAKING CHANGE` or `major:`
- **Minor version** (x.y.0): Commit messages containing `feat:` or `minor:`
- **Patch version** (x.y.z): All other commits (default)

### Manual Version Updates

You can also update versions manually using the script:

```bash
# Patch version (default)
node scripts/update-version.js

# Minor version
node scripts/update-version.js minor

# Major version
node scripts/update-version.js major
```

## Testing the Pipeline

### 1. Test Local Build

Before pushing, ensure the build works locally:

```bash
npm install --legacy-peer-deps
npm run build:dev
```

### 2. Test Version Script

Test the version update script:

```bash
node scripts/update-version.js patch
```

### 3. Create Test Branch

Create a test branch from `develop`:

```bash
git checkout develop
git checkout -b test/ci-cd-pipeline
```

### 4. Make Test Changes

Make a small change and commit with different message types:

```bash
# For patch version
git commit -m "fix: update footer styling"

# For minor version
git commit -m "feat: add new component"

# For major version
git commit -m "feat: major redesign BREAKING CHANGE: removed old API"
```

### 5. Push and Create PR

```bash
git push origin test/ci-cd-pipeline
```

Create a PR to `develop` branch and merge it to trigger the pipeline.

## Monitoring Deployments

### GitHub Actions

1. Go to your repository's **Actions** tab
2. Click on the latest workflow run
3. Monitor the deployment progress
4. Check the deployment summary at the bottom

### Firebase Console

1. Visit [Firebase Console](https://console.firebase.google.com/)
2. Select project `websitedemo-32483`
3. Go to **Hosting** section
4. View deployment history and status

### Live Website

After successful deployment, visit:
- **Live URL**: https://websitedemo-32483.web.app
- Check the footer for the updated version number

## Troubleshooting

### Common Issues

1. **Firebase Token Expired**
   - Regenerate token: `firebase login:ci`
   - Update GitHub secret `FIREBASE_TOKEN`

2. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are installed
   - Review build logs in GitHub Actions

3. **Permission Issues**
   - Ensure GitHub token has write permissions
   - Check Firebase project permissions

4. **Version Commit Failures**
   - Verify GitHub token permissions
   - Check if branch is protected

### Debug Commands

```bash
# Check Firebase projects
firebase projects:list

# Test Firebase deployment locally (using fixed project ID)
firebase deploy --only hosting --project websitedemo-32483

# Validate package.json
node -e "console.log(JSON.parse(require('fs').readFileSync('package.json', 'utf8')).version)"
```

## Pipeline Features

- ✅ Automatic triggering on develop branch
- ✅ Semantic version management
- ✅ Next.js build with legacy peer deps
- ✅ Firebase Hosting deployment
- ✅ Version display in footer
- ✅ Automatic version commits
- ✅ Deployment summaries
- ✅ Error handling and reporting

## Next Steps

1. Test the pipeline with a small change
2. Monitor the first few deployments
3. Set up branch protection rules if needed
4. Consider adding additional environments (staging, production)
5. Add notification integrations (Slack, email) if required
