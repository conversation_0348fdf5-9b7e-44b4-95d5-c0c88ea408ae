@use './aos-mixins' as *;

// AOS Default Settings
[data-aos] {
    transition-duration: 800ms;
    transition-timing-function: ease-out;

    &.aos-animate {
        transition-duration: 800ms;
    }
}

// Standard fade-up animation
[data-aos="fade-up"] {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
    transition-property: transform, opacity;

    &.aos-animate {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

// Standard zoom-in animation
[data-aos="zoom-in"] {
    opacity: 0;
    transform: scale(.6);
    transition-property: transform, opacity;

    &.aos-animate {
        opacity: 1;
        transform: scale(1);
    }
}

// Standard fade-down animation
[data-aos="fade-down"] {
    opacity: 0;
    transform: translate3d(0, -30px, 0);
    transition-property: transform, opacity;

    &.aos-animate {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

// Common delay classes
$delays: (
    100,
    200,
    300,
    400,
    500,
    600,
    700,
    800,
    900,
    1000
);

@each $delay in $delays {
    [data-aos][data-aos-delay="#{$delay}"] {
        transition-delay: #{$delay}ms;
    }
}

// Override duration for all AOS animations
[data-aos] {
    transition-duration: 800ms;
    transition-timing-function: ease-out;

    &.aos-animate {
        transition-duration: 800ms;
    }
}