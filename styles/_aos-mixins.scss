@use 'sass:math';

// AOS Animation Mixins
@mixin aos-base {
    [data-aos] {
        pointer-events: none;

        &.aos-animate {
            pointer-events: auto;
        }
    }
}

// Standardized fade up animation
@mixin aos-fade-up($delay: 0) {
    [data-aos="fade-up"] {
        opacity: 0;
        transform: translate3d(0, 30px, 0);
        transition-property: transform, opacity;

        &.aos-animate {
            opacity: 1;
            transform: translate3d(0, 0, 0);
        }

        @if $delay >0 {
            &[data-aos-delay="#{$delay}"] {
                transition-delay: #{$delay}ms;
            }
        }
    }
}

// Standardized zoom in animation
@mixin aos-zoom-in($delay: 0) {
    [data-aos="zoom-in"] {
        opacity: 0;
        transform: scale(.6);
        transition-property: transform, opacity;

        &.aos-animate {
            opacity: 1;
            transform: scale(1);
        }

        @if $delay >0 {
            &[data-aos-delay="#{$delay}"] {
                transition-delay: #{$delay}ms;
            }
        }
    }
}

// Standardized fade down animation
@mixin aos-fade-down($delay: 0) {
    [data-aos="fade-down"] {
        opacity: 0;
        transform: translate3d(0, -30px, 0);
        transition-property: transform, opacity;

        &.aos-animate {
            opacity: 1;
            transform: translate3d(0, 0, 0);
        }

        @if $delay >0 {
            &[data-aos-delay="#{$delay}"] {
                transition-delay: #{$delay}ms;
            }
        }
    }
}