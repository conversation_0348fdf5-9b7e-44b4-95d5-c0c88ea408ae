#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Updates the semantic version in package.json
 * Usage: node scripts/update-version.js [major|minor|patch]
 * Default: patch
 */

function updateVersion(versionType = 'patch') {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  
  try {
    // Read package.json
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const currentVersion = packageJson.version;
    
    // Parse current version
    const versionParts = currentVersion.split('.').map(Number);
    let [major, minor, patch] = versionParts;
    
    // Update version based on type
    switch (versionType.toLowerCase()) {
      case 'major':
        major += 1;
        minor = 0;
        patch = 0;
        break;
      case 'minor':
        minor += 1;
        patch = 0;
        break;
      case 'patch':
      default:
        patch += 1;
        break;
    }
    
    const newVersion = `${major}.${minor}.${patch}`;
    
    // Update package.json
    packageJson.version = newVersion;
    
    // Write back to package.json with proper formatting
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
    
    console.log(`Version updated from ${currentVersion} to ${newVersion}`);
    
    // Set output for GitHub Actions
    if (process.env.GITHUB_OUTPUT) {
      fs.appendFileSync(process.env.GITHUB_OUTPUT, `new_version=${newVersion}\n`);
      fs.appendFileSync(process.env.GITHUB_OUTPUT, `old_version=${currentVersion}\n`);
    }
    
    return newVersion;
  } catch (error) {
    console.error('Error updating version:', error.message);
    process.exit(1);
  }
}

// Get version type from command line arguments
const versionType = process.argv[2] || 'patch';
const validTypes = ['major', 'minor', 'patch'];

if (!validTypes.includes(versionType.toLowerCase())) {
  console.error(`Invalid version type: ${versionType}. Must be one of: ${validTypes.join(', ')}`);
  process.exit(1);
}

updateVersion(versionType);
