#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Test script to verify version functionality
 */

function testVersionUpdate() {
  console.log('🧪 Testing version update functionality...\n');
  
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  const originalPackageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const originalVersion = originalPackageJson.version;
  
  console.log(`📦 Original version: ${originalVersion}`);
  
  try {
    // Test patch update
    console.log('\n🔧 Testing patch update...');
    require('./update-version.js');
    
    // Read updated version
    const updatedPackageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const newVersion = updatedPackageJson.version;
    
    console.log(`✅ Version updated to: ${newVersion}`);
    
    // Verify version format
    const versionParts = newVersion.split('.').map(Number);
    if (versionParts.length === 3 && versionParts.every(part => !isNaN(part))) {
      console.log('✅ Version format is valid (x.y.z)');
    } else {
      throw new Error('Invalid version format');
    }
    
    // Test that version is actually different
    if (newVersion !== originalVersion) {
      console.log('✅ Version was successfully incremented');
    } else {
      throw new Error('Version was not incremented');
    }
    
    console.log('\n🎉 All tests passed!');
    
    // Restore original version for testing purposes
    originalPackageJson.version = originalVersion;
    fs.writeFileSync(packageJsonPath, JSON.stringify(originalPackageJson, null, 2) + '\n');
    console.log(`🔄 Restored original version: ${originalVersion}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    // Restore original version on error
    originalPackageJson.version = originalVersion;
    fs.writeFileSync(packageJsonPath, JSON.stringify(originalPackageJson, null, 2) + '\n');
    console.log(`🔄 Restored original version: ${originalVersion}`);
    
    process.exit(1);
  }
}

// Run tests
testVersionUpdate();
